<?php

namespace App\Http\Controllers;

use App\Exports\AppointementsExport;
use App\Exports\AppointmentsExport;
use App\Exports\BonusesExport;
use App\Exports\UsersExport;
use App\Models\Appointment;
use App\Models\AssistantRepresentativeAssignment;
use App\Models\Bonus;
use App\Models\Purchase;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class AdminController extends Controller
{
    /**
     * Global Dashboard - Overview of SA/SR Performance and Company Stats
     */
    public function dashboard()
    {
        $now = Carbon::now();
        $weekStart = $now->copy()->startOfWeek();
        $weekEnd = $now->copy()->endOfWeek();
        $monthStart = $now->copy()->startOfMonth();
        $monthEnd = $now->copy()->endOfMonth();

        // Sales Assistant Performance
        $saPerformance = $this->getSalesAssistantPerformance($weekStart, $weekEnd);

        // Sales Representative Performance
        $srPerformance = $this->getSalesRepresentativePerformance($weekStart, $weekEnd);

        // Company Stats
        $companyStats = $this->getCompanyStats($weekStart, $weekEnd, $monthStart, $monthEnd);

        // Real-time Leaderboards
        $leaderboards = $this->getLeaderboards();

        $test = User::where('role', 'assistant')
            ->withCount(['createdAppointments as weekly_appointments'])
            ->withSum(['bonuses as bonuses_total' => function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('created_at', [$weekStart, $weekEnd]);
            }], 'amount')
            ->orderByDesc('bonuses_total')
            ->take(5)
            ->get();

        // dd($test);
        // dd($test[0]->bonuses()->sum('amount'));

        return Inertia::render('AdminPages/Dashboard', [
            'saPerformance' => $saPerformance,
            'srPerformance' => $srPerformance,
            'companyStats' => $companyStats,
            'leaderboards' => $leaderboards,
        ]);
    }

    /**
     * Weekly Pairing System
     */
    public function pairings(Request $request)
    {
        $currentWeekPairings = $this->getCurrentWeekPairings();
        $pairingHistory = $this->getPairingHistory($request->get('page', 1));
        $topPerformingPairs = $this->getTopPerformingPairs();
        $availableRepresentatives = User::where('role', 'representative')->get();

        return Inertia::render('AdminPages/Pairings', [
            'currentWeekPairings' => $currentWeekPairings,
            'pairingHistory' => $pairingHistory,
            'topPerformingPairs' => $topPerformingPairs,
            'availableRepresentatives' => $availableRepresentatives,
        ]);
    }

    /**
     * Client & Appointment Database
     */
    public function appointments(Request $request)
    {
        $query = Appointment::with(['assistant', 'representative', 'purchases', 'bonuses']);

        // Apply filters
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        if ($request->filled('source')) {
            $query->where('source', $request->source);
        }
        if ($request->filled('assistant_id')) {
            $query->where('assistant_id', $request->assistant_id);
        }
        if ($request->filled('representative_id')) {
            $query->where('representative_id', $request->representative_id);
        }
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('clientName', 'like', "%{$search}%")
                    ->orWhere('clientPhone', 'like', "%{$search}%")
                    ->orWhere('clientAddress', 'like', "%{$search}%");
            });
        }

        $appointments = $query->orderBy('created_at', 'desc')->paginate(10);

        $assistants = User::where('role', 'assistant')->get();
        $representatives = User::where('role', 'representative')->get();

        return Inertia::render('AdminPages/Appointments', [
            'appointments' => $appointments,
            'assistants' => $assistants,
            'representatives' => $representatives,
            'filters' => $request->only(['date_from', 'date_to', 'source', 'assistant_id', 'representative_id', 'search']),
        ]);
    }

    /**
     * Bonus System Oversight
     */
    public function bonuses(Request $request)
    {
        $query = Bonus::with(['user', 'appointment']);

        // Apply filters
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $bonuses = $query->orderBy('created_at', 'desc')->paginate(20);

        $bonusSummary = $this->getBonusSummary();
        $assistants = User::where('role', 'assistant')->get();

        return Inertia::render('AdminPages/Bonuses', [
            'bonuses' => $bonuses,
            'bonusSummary' => $bonusSummary,
            'assistants' => $assistants,
            'filters' => $request->only(['date_from', 'date_to', 'user_id', 'type']),
        ]);
    }

    /**
     * Podium Management
     */
    public function podium()
    {
        $revenueLeaderboard = $this->getRevenueLeaderboard();
        $appointmentsLeaderboard = $this->getAppointmentsLeaderboard();
        $bonusesLeaderboard = $this->getBonusesLeaderboard();

        return Inertia::render('AdminPages/Podium', [
            'revenueLeaderboard' => $revenueLeaderboard,
            'appointmentsLeaderboard' => $appointmentsLeaderboard,
            'bonusesLeaderboard' => $bonusesLeaderboard,
        ]);
    }

    /**
     * User & Role Management
     */
    public function users(Request $request)
    {
        $query = User::query();

        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(8);

        $userStats = $this->getUserStats();

        return Inertia::render('AdminPages/Users', [
            'users' => $users,
            'userStats' => $userStats,
            'filters' => $request->only(['role', 'search']),
        ]);
    }





    // Helper methods for dashboard data
    private function getSalesAssistantPerformance($weekStart, $weekEnd)
    {
        $assistants = User::where('role', 'assistant')->get();

        return $assistants->map(function ($assistant) use ($weekStart, $weekEnd) {
            $weeklyAppointments = Appointment::where('assistant_id', $assistant->id)
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->count();

            $validatedAppointments = Appointment::where('assistant_id', $assistant->id)
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->whereHas('purchases', function ($q) {
                    $q->where('status', 'purchased');
                })
                ->count();

            $totalBonuses = Bonus::where('user_id', $assistant->id)
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->sum('amount');

            $dailyAppointments = Appointment::where('assistant_id', $assistant->id)
                ->whereDate('created_at', Carbon::today())
                ->count();

            return [
                'id' => $assistant->id,
                'name' => $assistant->name,
                'email' => $assistant->email,
                'daily_appointments' => $dailyAppointments,
                'weekly_appointments' => $weeklyAppointments,
                'validated_appointments' => $validatedAppointments,
                'total_bonuses' => $totalBonuses,
                'validation_rate' => $weeklyAppointments > 0 ? round(($validatedAppointments / $weeklyAppointments) * 100, 1) : 0,
            ];
        })->sortByDesc('total_bonuses')->values();
    }

    private function getSalesRepresentativePerformance($weekStart, $weekEnd)
    {
        $representatives = User::where('role', 'representative')->get();

        return $representatives->map(function ($rep) use ($weekStart, $weekEnd) {
            $weeklyRevenue = Purchase::whereHas('appointment', function ($q) use ($rep) {
                $q->where('representative_id', $rep->id);
            })
                ->where('status', 'purchased')
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->sum('resale_price');

            $weeklyPurchases = Purchase::whereHas('appointment', function ($q) use ($rep) {
                $q->where('representative_id', $rep->id);
            })
                ->where('status', 'purchased')
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->count();

            $monthlyRevenue = Purchase::whereHas('appointment', function ($q) use ($rep) {
                $q->where('representative_id', $rep->id);
            })
                ->where('status', 'purchased')
                ->whereMonth('created_at', Carbon::now()->month)
                ->sum('resale_price');

            return [
                'id' => $rep->id,
                'name' => $rep->name,
                'email' => $rep->email,
                'weekly_revenue' => $weeklyRevenue,
                'weekly_purchases' => $weeklyPurchases,
                'monthly_revenue' => $monthlyRevenue,
            ];
        })->sortByDesc('weekly_revenue')->values();
    }

    private function getCompanyStats($weekStart, $weekEnd, $monthStart, $monthEnd)
    {
        $totalRevenue = Purchase::where('status', 'purchased')->sum('resale_price');
        $weeklyRevenue = Purchase::where('status', 'purchased')
            ->whereBetween('created_at', [$weekStart, $weekEnd])
            ->sum('resale_price');
        $monthlyRevenue = Purchase::where('status', 'purchased')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->sum('resale_price');
        $weeklyBenefit = Purchase::where('status', 'purchased')
            ->whereBetween('created_at', [$weekStart, $weekEnd])
            ->sum('benefit');

        //? Weekly net revenue (starting from -5,000 EUR base charge)
        $weeklyCharges = $weeklyRevenue - 5000;

        $totalPurchases = Purchase::where('status', 'purchased')->count();
        $weeklyPurchases = Purchase::where('status', 'purchased')
            ->whereBetween('created_at', [$weekStart, $weekEnd])
            ->count();

        return [
            'total_revenue' => $totalRevenue,
            'weekly_revenue' => $weeklyRevenue,
            'monthly_revenue' => $monthlyRevenue,
            'weeklyCharges' => $weeklyCharges,
            'weeklyBenefit' => $weeklyBenefit,
            'total_purchases' => $totalPurchases,
            'weekly_purchases' => $weeklyPurchases,
            'profit_loss' => $weeklyCharges > 0 ? 'profit' : 'loss',
        ];
    }

    private function getLeaderboards()
    {
        $now = Carbon::now();
        $weekStart = $now->copy()->startOfWeek();
        $weekEnd = $now->copy()->endOfWeek();

        // Revenue leaderboard (SR)
        $revenueLeaderboard = User::where('role', 'representative')
            ->withSum(['receivedAppointments as weekly_revenue' => function ($query) use ($weekStart, $weekEnd) {
                $query->join('purchases', 'appointments.id', '=', 'purchases.appointment_id')
                    ->where('purchases.status', 'purchased')
                    ->whereBetween('purchases.created_at', [$weekStart, $weekEnd]);
            }], 'purchases.resale_price')
            ->orderByDesc('weekly_revenue')
            ->take(5)
            ->get();

        // Appointments leaderboard (SA)
        $appointmentsLeaderboard = User::where('role', 'assistant')
            ->withCount(['createdAppointments as weekly_appointments' => function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('created_at', [$weekStart, $weekEnd]);
            }])
            ->orderByDesc('weekly_appointments')
            ->take(5)
            ->get();

        // Bonuses leaderboard (SA)
        $bonusesLeaderboard = User::where('role', 'assistant')
            ->withSum(['bonuses as weekly_bonuses' => function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('created_at', [$weekStart, $weekEnd]);
            }], 'amount')
            ->orderByDesc('weekly_bonuses')
            ->take(5)
            ->get();

        return [
            'revenue' => $revenueLeaderboard,
            'appointments' => $appointmentsLeaderboard,
            'bonuses' => $bonusesLeaderboard,
        ];
    }

    private function getCurrentWeekPairings()
    {
        $now = Carbon::now();
        $weekStart = $now->copy()->startOfWeek();
        $weekEnd = $now->copy()->endOfWeek();

        return DB::table('appointments')
            ->join('users as assistants', 'appointments.assistant_id', '=', 'assistants.id')
            ->join('users as representatives', 'appointments.representative_id', '=', 'representatives.id')
            ->leftJoin('purchases', 'appointments.id', '=', 'purchases.appointment_id')
            ->select(
                'assistants.id as assistant_id',
                'assistants.name as assistant_name',
                'representatives.id as representative_id',
                'representatives.name as representative_name',
                DB::raw('COUNT(DISTINCT appointments.id) as total_appointments'),
                DB::raw('COUNT(DISTINCT CASE WHEN purchases.status = "purchased" THEN appointments.id END) as successful_appointments'),
                DB::raw('SUM(CASE WHEN purchases.status = "purchased" THEN purchases.resale_price ELSE 0 END) as total_revenue')
            )
            ->whereBetween('appointments.created_at', [$weekStart, $weekEnd])
            ->groupBy('assistants.id', 'assistants.name', 'representatives.id', 'representatives.name')
            ->orderByRaw('SUM(CASE WHEN purchases.status = "purchased" THEN purchases.resale_price ELSE 0 END) DESC')
            ->get()
            ->map(function ($pairing) {
                $successRate = $pairing->total_appointments > 0
                    ? round(($pairing->successful_appointments / $pairing->total_appointments) * 100, 1)
                    : 0;

                return [
                    'assistant_id' => $pairing->assistant_id,
                    'assistant_name' => $pairing->assistant_name,
                    'representative_id' => $pairing->representative_id,
                    'representative_name' => $pairing->representative_name,
                    'total_appointments' => $pairing->total_appointments,
                    'successful_appointments' => $pairing->successful_appointments,
                    'total_revenue' => $pairing->total_revenue,
                    'success_rate' => $successRate,
                ];
            });
    }

    private function getPairingHistory($page = 1)
    {
        $perPage = 12; // Number of records per page

        $query = DB::table('appointments')
            ->join('users as assistants', 'appointments.assistant_id', '=', 'assistants.id')
            ->join('users as representatives', 'appointments.representative_id', '=', 'representatives.id')
            ->leftJoin('purchases', 'appointments.id', '=', 'purchases.appointment_id')
            ->select(
                'assistants.name as assistant_name',
                'representatives.name as representative_name',
                DB::raw('DATE(appointments.created_at) as date'),
                DB::raw('COUNT(DISTINCT appointments.id) as appointments_count'),
                DB::raw('COUNT(DISTINCT CASE WHEN purchases.status = "purchased" THEN appointments.id END) as purchases_count'),
                DB::raw('SUM(CASE WHEN purchases.status = "purchased" THEN purchases.resale_price ELSE 0 END) as revenue')
            )
            ->groupBy('assistants.name', 'representatives.name', DB::raw('DATE(appointments.created_at)'))
            ->orderBy('date', 'desc');


        // Get total count for pagination
        $total = $query->get()->count();

        // Apply pagination
        $offset = ($page - 1) * $perPage;
        $data = $query->offset($offset)->limit($perPage)->get();

        return [
            'data' => $data,
            'current_page' => (int) $page,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total),
        ];
    }

    private function getTopPerformingPairs()
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        return DB::table('appointments')
            ->join('users as assistants', 'appointments.assistant_id', '=', 'assistants.id')
            ->join('users as representatives', 'appointments.representative_id', '=', 'representatives.id')
            ->leftJoin('purchases', 'appointments.id', '=', 'purchases.appointment_id')
            ->select(
                'assistants.name as assistant_name',
                'representatives.name as representative_name',
                DB::raw('COUNT(DISTINCT appointments.id) as total_appointments'),
                DB::raw('COUNT(DISTINCT CASE WHEN purchases.status = "purchased" THEN appointments.id END) as successful_appointments'),
                DB::raw('SUM(CASE WHEN purchases.status = "purchased" THEN purchases.resale_price ELSE 0 END) as total_revenue')
            )
            ->where('appointments.created_at', '>=', $thirtyDaysAgo)
            ->groupBy('assistants.name', 'representatives.name')
            ->orderByRaw('SUM(CASE WHEN purchases.status = "purchased" THEN purchases.resale_price ELSE 0 END) DESC')
            ->limit(10)
            ->get()
            ->map(function ($pair) {
                $successRate = $pair->total_appointments > 0
                    ? round(($pair->successful_appointments / $pair->total_appointments) * 100, 1)
                    : 0;

                return [
                    'assistant_name' => $pair->assistant_name,
                    'representative_name' => $pair->representative_name,
                    'total_appointments' => $pair->total_appointments,
                    'successful_appointments' => $pair->successful_appointments,
                    'total_revenue' => $pair->total_revenue,
                    'success_rate' => $successRate,
                ];
            });
    }

    private function getBonusSummary()
    {
        $today = Carbon::today();
        $weekStart = Carbon::now()->startOfWeek();
        $monthStart = Carbon::now()->startOfMonth();

        return [
            'daily_total' => Bonus::whereDate('created_at', $today)->sum('amount'),
            'weekly_total' => Bonus::where('created_at', '>=', $weekStart)->sum('amount'),
            'monthly_total' => Bonus::where('created_at', '>=', $monthStart)->sum('amount'),
            'daily_count' => Bonus::whereDate('created_at', $today)->count(),
            'weekly_count' => Bonus::where('created_at', '>=', $weekStart)->count(),
            'monthly_count' => Bonus::where('created_at', '>=', $monthStart)->count(),
            'by_type' => Bonus::select('type', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
                ->groupBy('type')
                ->get(),
            'by_source' => Bonus::join('appointments', 'bonuses.appointment_id', '=', 'appointments.id')
                ->select('appointments.source', DB::raw('COUNT(*) as count'), DB::raw('SUM(bonuses.amount) as total'))
                ->groupBy('appointments.source')
                ->get(),
        ];
    }

    private function getRevenueLeaderboard()
    {
        $weekStart = Carbon::now()->startOfWeek();
        $weekEnd = Carbon::now()->endOfWeek();

        return User::where('role', 'representative')
            ->select('users.*')
            ->leftJoin('appointments', 'users.id', '=', 'appointments.representative_id')
            ->leftJoin('purchases', 'appointments.id', '=', 'purchases.appointment_id')
            ->selectRaw('SUM(CASE WHEN purchases.status = "purchased" AND purchases.created_at BETWEEN ? AND ? THEN purchases.resale_price ELSE 0 END) as weekly_revenue', [$weekStart, $weekEnd])
            ->groupBy('users.id')
            ->orderByDesc('weekly_revenue')
            ->limit(10)
            ->get();
    }

    private function getAppointmentsLeaderboard()
    {
        $weekStart = Carbon::now()->startOfWeek();
        $weekEnd = Carbon::now()->endOfWeek();

        return User::where('role', 'assistant')
            ->withCount(['createdAppointments as weekly_appointments' => function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('created_at', [$weekStart, $weekEnd]);
            }])
            ->orderByDesc('weekly_appointments')
            ->limit(10)
            ->get();
    }

    private function getBonusesLeaderboard()
    {
        $weekStart = Carbon::now()->startOfWeek();
        $weekEnd = Carbon::now()->endOfWeek();

        return User::where('role', 'assistant')
            ->withSum(['bonuses as weekly_bonuses' => function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('created_at', [$weekStart, $weekEnd]);
            }], 'amount')
            ->orderByDesc('weekly_bonuses')
            ->limit(10)
            ->get();
    }

    private function getUserStats()
    {
        return [
            'total_users' => User::count(),
            'assistants' => User::where('role', 'assistant')->count(),
            'representatives' => User::where('role', 'representative')->count(),
            'recruiters' => User::where('role', 'recruiter')->count(),
            'executives' => User::where('role', 'executive')->count(),
            'admins' => User::where('role', 'admin')->count(),
            'recent_users' => User::where('created_at', '>=', Carbon::now()->subDays(7))->count(),
        ];
    }






    // Action methods for CRUD operations
    public function assignPairing(Request $request)
    {
        $request->validate([
            'assistant_id' => 'required|exists:users,id',
            'representative_id' => 'required|exists:users,id',
        ]);

        // Logic to assign pairing would go here
        // For now, we'll just return success
        return response()->json(['message' => 'Pairing assigned successfully']);
    }

    public function createUser(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email',
                'role' => 'required|in:assistant,representative,admin,recruiter,executive',
            ], [
                'email.unique' => 'Cette adresse email est déjà utilisée.',
                'email.required' => 'L\'adresse email est obligatoire.',
                'email.email' => 'L\'adresse email doit être valide.',
                'name.required' => 'Le nom est obligatoire.',
                'role.required' => 'Le rôle est obligatoire.',
            ]);

            // Generate a temporary password
            $temporaryPassword = \Illuminate\Support\Str::random(12);

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($temporaryPassword),
                'role' => $request->role,
                'is_activated' => true,
            ]);

            // Generate a password reset token for the new user
            $token = \Illuminate\Support\Facades\Password::createToken($user);

            // Create the reset URL
            $resetUrl = url('/reset-password/' . $token . '?email=' . urlencode($user->email));

            // Send the welcome notification with password setup
            $user->notify(new \App\Notifications\PasswordResetNotification($resetUrl));

            return redirect()->back()->with('success', "Utilisateur créé avec succès. Un email a été envoyé à {$user->email}");
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Return back with validation errors
            return redirect()->back()->withErrors($e->validator)->withInput();
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors de la création de l\'utilisateur');
        }
    }

    public function updateUser(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
        ]);

        // Only allow updating name and email for profile modifications
        $user->update($request->only(['name', 'email']));

        return redirect()->back()->with('success', 'Profil utilisateur mis à jour avec succès');
    }

    public function deleteUser($id)
    {
        $user = User::findOrFail($id);
        $user->delete();

        return response()->json(['message' => 'User deleted successfully']);
    }

    public function resetPassword(Request $request, $id)
    {
        $user = User::findOrFail($id);

        try {
            // Generate a password reset token
            $token = \Illuminate\Support\Facades\Password::createToken($user);
            // Create the reset URL
            $resetUrl = url('/reset-password/' . $token . '?email=' . urlencode($user->email));
            // Send the password reset notification
            $user->notify(new \App\Notifications\PasswordResetNotification($resetUrl));
            return back()->with('success', "Un email de réinitialisation de mot de passe a été envoyé à {$user->email}");
        } catch (\Exception $e) {
            return back()->with('error', "Erreur lors de la réinitialisation du mot de passe");
        }
    }

    public function toggleUserStatus(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);

            // Toggle the activation status
            $user->is_activated = !$user->is_activated;
            $user->save();

            $status = $user->is_activated ? 'activé' : 'désactivé';

            return redirect()->back()->with('success', "Utilisateur {$status} avec succès");
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors de la modification du statut utilisateur');
        }
    }

    public function updateBonus(Request $request, $id)
    {
        try {
            $bonus = Bonus::findOrFail($id);

            $request->validate([
                'amount' => 'required|integer|min:1',
                'type' => 'required|in:STANDARD,DAILY_SUPP',
                'reason' => 'nullable|string',
            ]);

            $bonus->update($request->only(['amount', 'type', 'reason']));

            return back()->with('success', 'Bonus modifié avec succès');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la modification du bonus', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors de la modification du bonus');
        }
    }

    public function deleteBonus($id)
    {
        try {
            $bonus = Bonus::findOrFail($id);
            $bonus->delete();

            return back()->with('success', 'Bonus supprimé avec succès');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression du bonus', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors de la suppression du bonus');
        }
    }

    public function exportAppointments(Request $request)
    {
        $month = $request->query('month', Carbon::now()->format('Y-m'));
        $year = $request->query('year', Carbon::now()->format('Y'));
        return Excel::download(new AppointmentsExport($month, $year), 'appointements_' . $month . '_' . $year . '_' . '.xlsx');
    }

    public function exportBonuses(Request $request)
    {
        $month = $request->query('month', Carbon::now()->format('Y-m'));
        $year = $request->query('year', Carbon::now()->format('Y'));
        return Excel::download(new BonusesExport($month, $year), 'bonuses' . $month . '_' . $year . '_' . '.xlsx');
    }

    // Assignment management methods
    public function getAssignments()
    {
        $assistants = User::where('role', 'assistant')
            ->with('assignedRepresentatives')
            ->get()
            ->map(function ($assistant) {
                return [
                    'id' => $assistant->id,
                    'name' => $assistant->name,
                    'email' => $assistant->email,
                    'assigned_representatives' => $assistant->assignedRepresentatives->map(function ($rep) {
                        return [
                            'id' => $rep->id,
                            'name' => $rep->name,
                            'email' => $rep->email,
                        ];
                    })->toArray()
                ];
            })->toArray();

        $representatives = User::where('role', 'representative')
            ->get()
            ->map(function ($rep) {
                return [
                    'id' => $rep->id,
                    'name' => $rep->name,
                    'email' => $rep->email,
                ];
            })->toArray();

        return response()->json([
            'assistants' => $assistants,
            'representatives' => $representatives,
        ]);
    }

    public function saveAssignments(Request $request)
    {
        try {
            \Log::info('Save assignments request received', ['data' => $request->all()]);

            $request->validate([
                'assignments' => 'required|array',
                'assignments.*.assistant_id' => 'required|exists:users,id',
                'assignments.*.representative_ids' => 'array',
                'assignments.*.representative_ids.*' => 'exists:users,id',
            ]);

            DB::transaction(function () use ($request) {
                foreach ($request->assignments as $assignment) {
                    $assistantId = $assignment['assistant_id'];
                    $representativeIds = $assignment['representative_ids'] ?? [];

                    \Log::info("Processing assignment for assistant {$assistantId}", [
                        'representative_ids' => $representativeIds
                    ]);

                    // Remove existing assignments for this assistant
                    $deletedCount = AssistantRepresentativeAssignment::where('assistant_id', $assistantId)->delete();
                    \Log::info("Deleted {$deletedCount} existing assignments for assistant {$assistantId}");

                    // Create new assignments
                    foreach ($representativeIds as $representativeId) {
                        $created = AssistantRepresentativeAssignment::create([
                            'assistant_id' => $assistantId,
                            'representative_id' => $representativeId,
                        ]);
                        \Log::info("Created assignment", ['id' => $created->id, 'assistant_id' => $assistantId, 'representative_id' => $representativeId]);
                    }
                }
            });

            \Log::info('Assignments saved successfully');
            return redirect()->back()->with('success', 'Assignations sauvegardées avec succès');
        } catch (\Exception $e) {
            \Log::error('Error saving assignments', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return redirect()->back()->with('error', 'Erreur lors de la sauvegarde des assignations: ' . $e->getMessage());
        }
    }
}
