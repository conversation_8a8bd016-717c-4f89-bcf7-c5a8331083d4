<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Purchase;
use App\Models\Bonus;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class RepresentativeController extends Controller
{
    /**
     * Apply weight adjustment for specific item categories
     */
    private function adjustWeightForWatches($weight, $itemType)
    {
        // Check if the item is watches and pocket watches in silver and gold
        if ($itemType === 'montre et montre a gousset (en argent et en or)') {
            // Apply 30% weight reduction (keep 70% of original weight)
            return $weight * 0.7;
        }

        return $weight;
    }

    /**
     * Create bonus for LeBonCoin purchases
     */
    private function createLeBonCoinBonus($appointmentId, $assistantId, $status)
    {
        // Check if bonus already exists for this appointment to avoid duplicates
        $existingBonus = Bonus::where('appointment_id', $appointmentId)
            ->where('reason', 'LBNC + Rachat')
            ->first();
        $purchases = Purchase::where('appointment_id', $appointmentId)->get();
        if (!$existingBonus) {
            Bonus::create([
                'user_id' => $assistantId,
                'appointment_id' => $appointmentId,
                'amount' => 10,
                'type' => 'STANDARD',
                'reason' => 'LBNC + Rachat'
            ]);
        } else {
            if ($status == 'not_purchased' && count($purchases) <= 1) {
                $existingBonus->delete();
            }
        }
    }
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'appointment_id' => 'required',
                'items' => 'required|array',
                'items.*.type' => 'required|string',
                'items.*.description' => 'nullable|string',
                'items.*.weight' => 'nullable|numeric',
                'items.*.purchasePrice' => 'nullable|numeric',
                'items.*.estimatedValue' => 'nullable|numeric',
                'items.*.hasTransaction' => 'required|boolean',
                'items.*.benefit' => 'nullable|numeric',
                'notes' => 'nullable|string'
            ]);

            // Get appointment details for bonus logic
            $appointment = Appointment::find($validated['appointment_id']);
            $hasPurchasedItems = false;

            // Create a purchase record for each item
            foreach ($validated['items'] as $item) {
                // Calculate individual benefit if not provided
                $purchasePrice = $item['purchasePrice'] ?? 0;
                $estimatedValue = $item['estimatedValue'] ?? 0;
                $benefit = $item['benefit'] ?? ($estimatedValue - $purchasePrice);

                // Apply weight adjustment for watches
                $adjustedWeight = $this->adjustWeightForWatches($item['weight'], $item['type']);

                // Track if any items were purchased
                if ($item['hasTransaction']) {
                    $hasPurchasedItems = true;
                }

                $purchase = Purchase::create([
                    'appointment_id' => $validated['appointment_id'],
                    'status' => $item['hasTransaction'] ? 'purchased' : 'not_purchased',
                    'description' => $item['description'],
                    'item_type' => $item['type'],
                    'weight' => $adjustedWeight,
                    'buy_price' => $purchasePrice,
                    'resale_price' => $estimatedValue,
                    'benefit' => $benefit,
                    'notes' => $validated['notes']
                ]);
            }

            // Create LeBonCoin bonus if applicable
            if ($hasPurchasedItems && $appointment && $appointment->source === 'leboncoin') {
                $this->createLeBonCoinBonus($appointment->id, $appointment->assistant_id, $purchase->status);
            }

            return back()->with(['message' => 'post rdv created successfully']);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la création du post-RDV', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors de la création du post-RDV');
        }
    }
    public function update(Request $request, Purchase $purchase)
    {
        // dd($purchase);
        try {
            $validated = $request->validate([
                'description' => 'nullable|string',
                'type' => 'nullable|string',
                'weight' => 'nullable|numeric',
                'buy_price' => 'nullable|numeric',
                'resale_price' => 'nullable|numeric',
                'benefit' => 'nullable|numeric',
                'status' => 'nullable|string|in:purchased,not_purchased',
                'notes' => 'nullable|string'
            ]);

            // Calculate benefit from the new values
            $purchasePrice = floatval($validated['buy_price'] ?? $purchase->buy_price ?? 0);
            $estimatedValue = floatval($validated['resale_price'] ?? $purchase->resale_price ?? 0);
            $benefit = $estimatedValue - $purchasePrice;

            // Apply weight adjustment for watches if type is being updated
            $itemType = $validated['type'] ?? $purchase->item_type;
            $weight = $validated['weight'] ? floatval($validated['weight']) : $purchase->weight;
            $adjustedWeight = $this->adjustWeightForWatches($weight, $itemType);

            $updateData = [
                'description' => $validated['description'],
                'item_type' => $itemType,
                'weight' => $adjustedWeight,
                'buy_price' => $purchasePrice,
                'resale_price' => $estimatedValue,
                'benefit' => $benefit,
                'notes' => $validated['notes']
            ];

            // Only update status if provided
            if (isset($validated['status'])) {
                $updateData['status'] = $validated['status'];
            }

            // Check if status is being changed to 'purchased' for bonus logic
            $statusChanged = isset($validated['status']) &&
                $validated['status'] !== $purchase->status;

            $result = $purchase->update($updateData);

            // Create LeBonCoin bonus if status changed to purchased
            if ($result && $statusChanged) {
                $appointment = $purchase->appointment;
                if ($appointment && $appointment->source === 'leboncoin') {
                    $this->createLeBonCoinBonus($appointment->id, $appointment->assistant_id, $validated['status']);
                }
            }
            if ($result) {
                return back()->with(['success' => 'Post RDV updated successfully']);
            } else {
                return back()->with(['error' => 'Failed to update Post RDV']);
            }
        } catch (\Exception $e) {
            return back()->with(['error' => 'Update failed: ' . $e->getMessage()]);
        }
    }
    public function dashboardPage()
    {
        try {
            $user = Auth::user();
            $now = Carbon::now();

            // Today's date for filtering
            $todayDate = $now->format('Y-m-d');

            // Weekly stats
            $weekStart = $now->copy()->startOfWeek()->format('Y-m-d H:i:s');
            $weekEnd = $now->copy()->endOfWeek()->format('Y-m-d H:i:s');

            // Monthly stats
            $monthStart = $now->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $monthEnd = $now->copy()->endOfMonth()->format('Y-m-d H:i:s');

            // Today's appointments - using whereDate for better compatibility
            $todayAppointments = Appointment::where('representative_id', $user->id)
                ->whereDate('dateTime', $todayDate)
                ->orderBy('dateTime', 'asc')
                ->get();

            // Recent purchases (last 5)
            $recentPurchases = Purchase::with('appointment')
                ->whereHas('appointment', function ($query) use ($user) {
                    $query->where('representative_id', $user->id);
                })
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($purchase) {
                    return [
                        'id' => $purchase->id,
                        'clientName' => $purchase->appointment->clientName ?? 'N/A',
                        'type' => $purchase->item_type,
                        'amount' => $purchase->resale_price,
                        'date' => Carbon::parse($purchase->created_at)->format('Y-m-d'),
                        'status' => $purchase->status,
                    ];
                });

            // Stats for dashboard cards
            $stats = [
                'today' => [
                    'appointments' => $todayAppointments->count(),
                    'completed' => Purchase::whereHas('appointment', function ($q) use ($user, $todayDate) {
                        $q->where('representative_id', $user->id)
                            ->whereDate('created_at', $todayDate);
                    })->where('status', 'purchased')->count(),
                    'revenue' => Purchase::whereHas('appointment', function ($q) use ($user, $todayDate) {
                        $q->where('representative_id', $user->id);
                            // ->whereDate('created_at', $todayDate);
                    })->where('status', 'purchased')->whereDate("created_at", $todayDate)
                    ->sum('resale_price')?? 0,
                ],
                'week' => [
                    'appointments' => Appointment::where('representative_id', $user->id)
                        ->whereBetween('dateTime', [$weekStart, $weekEnd])
                        ->count(),
                    'purchases' => Purchase::whereHas('appointment', function ($q) use ($user) {
                        $q->where('representative_id', $user->id);
                    })
                        ->whereBetween('created_at', [$weekStart, $weekEnd])
                        ->where('status', 'purchased')
                        ->count(),
                    'revenue' => Purchase::whereHas('appointment', function ($q) use ($user) {
                        $q->where('representative_id', $user->id);
                    })
                        ->whereBetween('created_at', [$weekStart, $weekEnd])
                        ->where('status', 'purchased')
                        ->sum('resale_price') ?? 0,
                ],
                'month' => [
                    'appointments' => Appointment::where('representative_id', $user->id)
                        ->whereBetween('dateTime', [$monthStart, $monthEnd])
                        ->count(),
                    'purchases' => Purchase::whereHas('appointment', function ($q) use ($user) {
                        $q->where('representative_id', $user->id);
                    })
                        ->whereBetween('created_at', [$monthStart, $monthEnd])
                        ->where('status', 'purchased')
                        ->count(),
                    'revenue' => Purchase::whereHas('appointment', function ($q) use ($user) {
                        $q->where('representative_id', $user->id);
                    })
                        ->whereBetween('created_at', [$monthStart, $monthEnd])
                        ->where('status', 'purchased')
                        ->sum('resale_price') ?? 0,
                ],
            ];

            return Inertia::render('RepresentativePages/dashboard', [
                'todayAppointments' => $todayAppointments,
                'recentPurchases' => $recentPurchases,
                'stats' => $stats,

            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement du tableau de bord représentant', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement du tableau de bord');
        }
    }
    public function calendarPage()
    {
        try {
            return Inertia::render('RepresentativePages/calendarPage', [
                'appointments' => Appointment::where('representative_id', Auth::id())->orderBy('dateTime', 'asc')->with('purchases')->get(),
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement du calendrier', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement du calendrier');
        }
    }
    public function historyPage(Request $request)
    {
        try {
            $user = Auth::user();
            $perPage = $request->get('per_page', 15); // Default 15 items per page

            // Get paginated purchases with related appointment data
            $purchasesQuery = Purchase::with('appointment')
                ->whereHas('appointment', function ($query) use ($user) {
                    $query->where('representative_id', $user->id);
                })
                // ->where('status', 'purchased')  // Only include purchased items
                ->orderBy('created_at', 'desc');

            $paginatedPurchases = $purchasesQuery->paginate($perPage);

            $purchases = $paginatedPurchases->getCollection()->map(function ($purchase) {
                return [
                    'id' => $purchase->id,
                    'date' => Carbon::parse($purchase->created_at)->format('Y-m-d'),
                    'time' => Carbon::parse($purchase->created_at)->format('H:i'),
                    'clientName' => $purchase->appointment->clientName ?? 'N/A',
                    'type' => $purchase->item_type,
                    'items' => [[
                        'description' => $purchase->description,
                        'weight' => $purchase->weight,
                        'purchasePrice' => $purchase->buy_price,
                        'estimatedValue' => $purchase->resale_price,
                    ]],
                    'totalPurchase' => $purchase->buy_price,
                    'totalEstimated' => $purchase->resale_price,
                    'profit' => $purchase->benefit,
                    'margin' => $purchase->buy_price > 0
                        ? (($purchase->resale_price - $purchase->buy_price) / $purchase->resale_price) * 100
                        : 0,
                    'hasNextAppointment' => rand(0, 1) === 1, // Random for demo
                    'status' => $purchase->status,
                ];
            });

            // Update the paginated collection with transformed data
            $paginatedPurchases->setCollection($purchases);
            $mabi3ats = Purchase::with('appointment')
                ->whereHas('appointment', function ($query) use ($user) {
                    $query->where('representative_id', $user->id);
                })
                ->where('status', 'purchased');
            // $bnft = $mabi3ats->sum('resale_price') - $mabi3ats->sum('buy_price') ;
            $mg =
                (($mabi3ats->sum('resale_price') - $mabi3ats->sum('buy_price')) / $mabi3ats->sum('resale_price')) * 100;
            // dd($mg);

            // Calculate totals
            $totalStats = [
                'totalEntries' => $purchases->count(),
                'totalPurchase' => $purchases->where('status', 'purchased')->sum('totalPurchase'),
                'totalBuy' => $purchases->where('status', 'purchased')->sum('totalEstimated'),
                'totalProfit' => $purchases->where('status', 'purchased')->sum('profit'),
                'avgMargin' => $mg ?? 0,
            ];

            return Inertia::render('RepresentativePages/historyPage', [
                'historyData' => $purchases,
                'totalStats' => $totalStats,
                'pagination' => [
                    'current_page' => $paginatedPurchases->currentPage(),
                    'last_page' => $paginatedPurchases->lastPage(),
                    'per_page' => $paginatedPurchases->perPage(),
                    'total' => $paginatedPurchases->total(),
                    'from' => $paginatedPurchases->firstItem(),
                    'to' => $paginatedPurchases->lastItem(),
                    'links' => $paginatedPurchases->links()->elements[0] ?? [],
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement de l\'historique', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement de l\'historique');
        }
    }

    public function objectifsPage()
    {
        try {
            $user = Auth::user();
            $now = Carbon::now();
            $startOfWeek = $now->copy()->startOfWeek()->format('Y-m-d H:i:s');
            $endOfWeek = $now->copy()->endOfWeek()->format('Y-m-d H:i:s');

            // Leaderboard
            $leaderboard = $this->getLeaderboard($startOfWeek, $endOfWeek, $user->id);

            return Inertia::render('RepresentativePages/objectivesPage', [
                'leaderboard' => $leaderboard,
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des objectifs', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement des objectifs');
        }
    }

    protected function getWeeklyRevenue($userId, $startDate, $endDate)
    {
        return Purchase::whereHas('appointment', function ($query) use ($userId) {
            $query->where('representative_id', $userId);
        })
            ->where('status', 'purchased')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('resale_price') ?? 0;
    }

    protected function getWeeklyPurchases($userId, $startDate, $endDate)
    {
        return Purchase::whereHas('appointment', function ($query) use ($userId) {
            $query->where('representative_id', $userId);
        })
            ->where('status', 'purchased')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
    }

    protected function getWeeklyNewClients($userId, $startDate, $endDate)
    {
        return Appointment::where('representative_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('clientPhone')
            ->count('clientPhone');
    }

    protected function getWeeklyAppointments($userId, $startDate, $endDate)
    {
        return Appointment::where('representative_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
    }

    protected function getMonthlyRevenue($userId, $startDate, $endDate)
    {
        return Purchase::whereHas('appointment', function ($query) use ($userId) {
            $query->where('representative_id', $userId);
        })
            ->where('status', 'purchased')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('resale_price') ?? 0;
    }

    protected function getMonthlyPurchases($userId, $startDate, $endDate)
    {
        return Purchase::whereHas('appointment', function ($query) use ($userId) {
            $query->where('representative_id', $userId);
        })
            ->where('status', 'purchased')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
    }

    protected function getConversionRate($userId, $startDate, $endDate)
    {
        $appointments = Appointment::where('representative_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $purchases = $this->getMonthlyPurchases($userId, $startDate, $endDate);

        return $appointments > 0 ? round(($purchases / $appointments) * 100, 2) : 0;
    }

    protected function getLeaderboard($startDate, $endDate, $currentUserId)
    {
        $isSqlite = DB::getDriverName() === 'sqlite';

        $initialsExpr = $isSqlite
            ? DB::raw("UPPER(substr(users.name, 1, 1) || CASE WHEN instr(users.name, ' ') > 0 THEN substr(users.name, instr(users.name, ' ') + 1, 1) ELSE '' END) as initials")
            : DB::raw("CONCAT(SUBSTRING(users.name, 1, 1), SUBSTRING(users.name, LOCATE(' ', users.name) + 1, 1)) as initials");

        return DB::table('purchases')
            ->join('appointments', 'appointments.id', '=', 'purchases.appointment_id')
            ->join('users', 'users.id', '=', 'appointments.representative_id')
            ->select(
                'users.id',
                'users.name',
                DB::raw('SUM(purchases.resale_price) as total_revenue'),
                DB::raw('COUNT(purchases.id) as total_purchases'),
                $initialsExpr
            )
            ->where('purchases.status', 'purchased')
            ->whereBetween('purchases.created_at', [$startDate, $endDate])
            ->groupBy('users.id', 'users.name')
            ->orderByDesc('total_revenue')
            ->limit(5)
            ->get()
            ->map(function ($item, $index) use ($currentUserId) {
                return [
                    'rank' => $index + 1,
                    'name' => $item->name,
                    'avatar' => $item->initials,
                    'ca' => $item->total_revenue,
                    'rachats' => $item->total_purchases,
                    'trend' => '+' . rand(3, 15) . '%', // Mock trend
                    'badge' => $index < 3 ? ['🥇', '🥈', '🥉'][$index] : '',
                    'isCurrentUser' => $item->id == $currentUserId,
                ];
            });
    }
}
