import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { Link } from '@inertiajs/react';
import {
    BookOpen,
    CalendarPlus,
    Folder,
    HandCoins,
    History,
    LayoutDashboard,
    LayoutGrid,
    TrendingUp,
    Users,
    Settings,
    Database,
    Award,
    Shield
} from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems = [
    // ? Sales Assitant links
    {
        title: 'Tableau de bord',
        url: '/welcomeAssistant',
        icon: LayoutDashboard,
        permission: 'assistant'
    },
    {
        title: 'Création de rendez-vous',
        url: '/appointmentCreation',
        icon: CalendarPlus,
        permission: 'assistant'
    },
    {
        title: 'Historique des rendez-vous',
        url: '/appointmentsHistory',
        icon: History,
        permission: 'assistant'
    },
    {
        title: 'Performance',
        url: '/assistantPerformance',
        icon: TrendingUp,
        permission: 'assistant'
    },
    // ? End Sales Assitant links
    {
        title: 'Tableau de bord',
        url: '/representative/dashboard',
        icon: LayoutDashboard,
        permission: 'representative'
    },
    {
        title: 'Calendrier',
        url: '/representative/calendar',
        icon: CalendarPlus,
        permission: 'representative'
    },
    {
        title: 'Performance',
        url: '/representative/performance',
        icon:  TrendingUp,
        permission: 'representative'
    },
    {
        title: 'Historique',
        url: '/representative/history',
        icon: History ,
        permission: 'representative'
    },
    {
        title: 'Tableau de bord',
        url: '/recruiter/dashboard',
        icon: LayoutDashboard,
        permission: 'recruiter'
    },
    {
        title: 'Performance',
        url: '/recruiter/performance',
        icon: TrendingUp,
        permission: 'recruiter'
    },
    {
        title: 'Appariement',
        url: '/recruiter/pairings',
        icon: History,
        permission: 'recruiter'
    },
    {
        title: 'Tableau de bord',
        url: '/welcomeExecutive',
        icon: LayoutDashboard,
        permission: 'executive'
    },
    {
        title: 'Paie',
        url: '/payrollPage',
        icon: HandCoins,
        permission: 'executive'
    },
    // ? Admin links
    {
        title: 'Tableau de bord global',
        url: '/admin/dashboard',
        icon: LayoutDashboard,
        permission: 'admin'
    },
    {
        title: 'Système de pairing',
        url: '/admin/pairings',
        icon: Users,
        permission: 'admin'
    },
    {
        title: 'Base de données RDV',
        url: '/admin/appointments',
        icon: Database,
        permission: 'admin'
    },
    {
        title: 'Système de bonus',
        url: '/admin/bonuses',
        icon: Award,
        permission: 'admin'
    },
    {
        title: 'Gestion du podium',
        url: '/admin/podium',
        icon: TrendingUp,
        permission: 'admin'
    },
    {
        title: 'Gestion utilisateurs',
        url: '/admin/users',
        icon: Shield,
        permission: 'admin'
    },
    // ? End Admin links
];

// const footerNavItems = [
//     {
//         title: 'Repository',
//         url: 'https://github.com/laravel/react-starter-kit',
//         icon: Folder,
//     },
//     {
//         title: 'Documentation',
//         url: 'https://laravel.com/docs/starter-kits',
//         icon: BookOpen,
//     },
// ];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                {/* <NavFooter items={footerNavItems} className="mt-auto" /> */}
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
