import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { router } from '@inertiajs/react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

export function Pagination({ pagination, onPageChange, showPerPageSelector = true }) {
    if (!pagination || pagination.last_page <= 1) {
        return null;
    }

    const { current_page, last_page, per_page, total, from, to } = pagination;

    const handlePageChange = (page) => {
        if (page >= 1 && page <= last_page && page !== current_page) {
            if (onPageChange) {
                onPageChange(page);
            } else {
                // Default behavior using Inertia router
                const url = new URL(window.location);
                url.searchParams.set('page', page);
                router.get(url.toString(), {}, { preserveState: true, preserveScroll: true });
            }
        }
    };

    const handlePerPageChange = (newPerPage) => {
        const url = new URL(window.location);
        url.searchParams.set('per_page', newPerPage);
        url.searchParams.delete('page'); // Reset to first page when changing per_page
        router.get(url.toString(), {}, { preserveState: true, preserveScroll: true });
    };

    const generatePageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 5;
        
        if (last_page <= maxVisiblePages) {
            // Show all pages if total pages is small
            for (let i = 1; i <= last_page; i++) {
                pages.push(i);
            }
        } else {
            // Always show first page
            pages.push(1);
            
            let start = Math.max(2, current_page - 1);
            let end = Math.min(last_page - 1, current_page + 1);
            
            // Add ellipsis after first page if needed
            if (start > 2) {
                pages.push('...');
            }
            
            // Add middle pages
            for (let i = start; i <= end; i++) {
                if (i !== 1 && i !== last_page) {
                    pages.push(i);
                }
            }
            
            // Add ellipsis before last page if needed
            if (end < last_page - 1) {
                pages.push('...');
            }
            
            // Always show last page
            if (last_page > 1) {
                pages.push(last_page);
            }
        }
        
        return pages;
    };

    const pageNumbers = generatePageNumbers();

    return (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 px-2">
            {/* Results info */}
            <div className="text-sm text-[#525e62]/70">
                Affichage de <span className="font-medium">{from}</span> à{' '}
                <span className="font-medium">{to}</span> sur{' '}
                <span className="font-medium">{total}</span> résultats
            </div>

            <div className="flex items-center gap-4">
                {/* Per page selector */}
                {showPerPageSelector && (
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-[#525e62]/70">Afficher:</span>
                        <Select value={per_page.toString()} onValueChange={handlePerPageChange}>
                            <SelectTrigger className="w-20 h-8">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="15">15</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                )}

                {/* Pagination controls */}
                <div className="flex items-center gap-1">
                    {/* Previous button */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(current_page - 1)}
                        disabled={current_page <= 1}
                        className="h-8 w-8 p-0"
                    >
                        <ChevronLeft className="h-4 w-4" />
                    </Button>

                    {/* Page numbers */}
                    {pageNumbers.map((page, index) => (
                        <div key={index}>
                            {page === '...' ? (
                                <div className="flex h-8 w-8 items-center justify-center">
                                    <MoreHorizontal className="h-4 w-4 text-[#525e62]/50" />
                                </div>
                            ) : (
                                <Button
                                    variant={page === current_page ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handlePageChange(page)}
                                    className="h-8 w-8 p-0"
                                >
                                    {page}
                                </Button>
                            )}
                        </div>
                    ))}

                    {/* Next button */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(current_page + 1)}
                        disabled={current_page >= last_page}
                        className="h-8 w-8 p-0"
                    >
                        <ChevronRight className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    );
}
