import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm, usePage } from '@inertiajs/react';
import {
    Calendar,
    Clock,
    Download,
    Edit,
    Eye,
    Filter,
    MapPin,
    Phone,
    Search,
    Trash2,
    User,
    ChevronLeft,
    ChevronRight,
    Info,
    AlertCircle
} from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Base de données RDV',
        href: '/admin/appointments',
    },
];

export default function AdminAppointments() {
    const { data, setData, post, processing } = useForm();
    const { appointments, assistants, representatives, filters } = usePage().props;
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [dateFrom, setDateFrom] = useState(filters?.date_from || '');
    const [dateTo, setDateTo] = useState(filters?.date_to || '');
    const [selectedSource, setSelectedSource] = useState(filters?.source || '');
    const [selectedAssistant, setSelectedAssistant] = useState(filters?.assistant_id || '');
    const [selectedRepresentative, setSelectedRepresentative] = useState(filters?.representative_id || '');
    const [showFilters, setShowFilters] = useState(false);
    const [exportMonth, setExportMonth] = useState('');
    const [exportYear, setExportYear] = useState('');



    const handleSearch = () => {
        router.get(
            '/admin/appointments',
            {
                search: searchTerm,
                date_from: dateFrom,
                date_to: dateTo,
                source: selectedSource,
                assistant_id: selectedAssistant,
                representative_id: selectedRepresentative,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const handleExport = () => {
        if (!exportMonth || !exportYear) return;
        window.location.href = `/admin/appointments/export?month=${exportMonth}&year=${exportYear}`;
    };

    const clearFilters = () => {
        setSearchTerm('');
        setDateFrom('');
        setDateTo('');
        setSelectedSource('');
        setSelectedAssistant('');
        setSelectedRepresentative('');
        router.get('/admin/appointments');
    };

    const getSourceBadge = (source) => {
        const sourceConfig = {
            leboncoin: {
                label: 'LBC',
                mobileLabel: 'LBC',
                className: 'bg-blue-50 text-blue-700 border-blue-200'
            },
            outbound: {
                label: 'APPEL',
                mobileLabel: 'TEL',
                className: 'bg-green-50 text-green-700 border-green-200'
            },
        };

        const config = sourceConfig[source] || {
            label: source,
            mobileLabel: source?.substring(0, 3).toUpperCase(),
            className: 'bg-gray-50 text-gray-700 border-gray-200'
        };

        return (
            <Badge variant="outline" className={`${config.className} text-[10px] xs:text-xs font-medium px-1.5 xs:px-2 py-0.5`}>
                <span className="xs:hidden">{config.mobileLabel}</span>
                <span className="hidden xs:inline">{config.label}</span>
            </Badge>
        );
    };

    const getStatusBadge = (appointment) => {
        // Check if appointment has purchases (can be used for additional styling)
        // const hasPurchase = appointment.purchases && appointment.purchases.length > 0;

        if (appointment.appointment_type === 'announced') {
            return (
                <Badge className="bg-green-50 text-green-700 border-green-200 text-[10px] xs:text-xs font-medium px-1.5 xs:px-2 py-0.5">
                    <span className="xs:hidden">Annoncé</span>
                    <span className="hidden xs:inline">Annoncé (d'après SA)</span>
                </Badge>
            );
        } else {
            return (
                <Badge className="bg-red-50 text-red-700 border-red-200 text-[10px] xs:text-xs font-medium px-1.5 xs:px-2 py-0.5">
                    <span className="xs:hidden">Non Annoncé</span>
                    <span className="hidden xs:inline">Non Annoncé</span>
                </Badge>
            );
        }
    };

    const AppointmentCard = ({ appointment }) => (
        <Card className="border-0 bg-white shadow-sm transition-all duration-200 hover:shadow-md hover:scale-[1.01]">
            <CardContent className="p-3 xs:p-4 sm:p-5 md:p-6">
                {/* Header Section */}
                <div className="mb-3 xs:mb-4 flex flex-col space-y-3 xs:space-y-0 xs:flex-row xs:items-start xs:justify-between">
                    <div className="flex min-w-0 flex-1 items-center space-x-2 xs:space-x-3">
                        <div className="flex h-10 w-10 xs:h-11 xs:w-11 sm:h-12 sm:w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100">
                            <Calendar className="h-4 w-4 xs:h-5 xs:w-5 sm:h-6 sm:w-6 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="text-sm xs:text-base sm:text-lg font-semibold text-gray-900 leading-tight">
                                {appointment.clientName}
                            </h3>
                            <p className="mt-1 flex items-center text-xs xs:text-sm text-gray-500">
                                <Clock className="mr-1 h-3 w-3 xs:h-4 xs:w-4 flex-shrink-0" />
                                <span className="truncate">
                                    {/* Mobile: Show short date format */}
                                    <span className="xs:hidden">
                                        {new Date(appointment.dateTime).toLocaleDateString('fr-FR', {
                                            day: '2-digit',
                                            month: '2-digit',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                    </span>
                                    {/* Desktop: Show full date format */}
                                    <span className="hidden xs:inline">
                                        {new Date(appointment.dateTime).toLocaleString('fr-FR')}
                                    </span>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div className="flex flex-shrink-0 items-center gap-1 xs:gap-2 xs:flex-col xs:items-end sm:flex-row sm:items-center">
                        {getSourceBadge(appointment.source)}
                        {getStatusBadge(appointment)}
                    </div>
                </div>

                {/* Contact & Assignment Info */}
                <div className="mb-3 xs:mb-4 grid grid-cols-1 xs:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 bg-gray-50 p-2 xs:p-3 rounded-lg">
                    <div className="space-y-2">
                        <div className="flex items-center text-xs xs:text-sm text-gray-700">
                            <div className="bg-blue-50 p-1 rounded-full mr-2 flex-shrink-0">
                                <Phone className="h-3 w-3 xs:h-4 xs:w-4 text-blue-600" />
                            </div>
                            <span className="truncate font-medium">{appointment.clientPhone}</span>
                        </div>
                        <div className="flex items-start text-xs xs:text-sm text-gray-700">
                            <div className="bg-blue-50 p-1 rounded-full mr-2 flex-shrink-0 mt-0.5">
                                <MapPin className="h-3 w-3 xs:h-4 xs:w-4 text-blue-600" />
                            </div>
                            <span className="line-clamp-2">{appointment.clientAddress}</span>
                        </div>
                    </div>
                    <div className="space-y-2 mt-2 xs:mt-0">
                        <div className="flex items-center text-xs xs:text-sm text-gray-700">
                            <div className="bg-purple-50 p-1 rounded-full mr-2 flex-shrink-0">
                                <User className="h-3 w-3 xs:h-4 xs:w-4 text-purple-600" />
                            </div>
                            <span className="truncate">
                                <span className="text-[10px] xs:text-xs text-purple-600 font-medium mr-1">Assistant:</span>
                                {appointment.assistant?.name}
                            </span>
                        </div>
                        <div className="flex items-center text-xs xs:text-sm text-gray-700">
                            <div className="bg-green-50 p-1 rounded-full mr-2 flex-shrink-0">
                                <User className="h-3 w-3 xs:h-4 xs:w-4 text-green-600" />
                            </div>
                            <span className="truncate">
                                <span className="text-[10px] xs:text-xs text-green-600 font-medium mr-1">Commercial:</span>
                                {appointment.representative?.name}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Notes Section */}
                {appointment.notes && (
                    <div className="mb-3 xs:mb-4">
                        <div className="rounded-lg bg-blue-50 border border-blue-100 p-2 xs:p-3">
                            <div className="flex items-start space-x-2">
                                <div className="bg-blue-100 p-1 rounded-full flex-shrink-0 mt-0.5">
                                    <Info className="h-3 w-3 xs:h-4 xs:w-4 text-blue-600" />
                                </div>
                                <div className="flex-1">
                                    <p className="text-xs xs:text-sm text-blue-800 font-medium mb-1">Notes:</p>
                                    <p className="text-xs xs:text-sm text-blue-700 leading-relaxed">{appointment.notes}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Purchases Section */}
                {appointment.purchases && appointment.purchases.length > 0 && (
                    <div className="mb-3 xs:mb-4">
                        <div className="flex items-center mb-2 xs:mb-3">
                            <div className="bg-green-100 p-1 rounded-full mr-2">
                                <Calendar className="h-3 w-3 xs:h-4 xs:w-4 text-green-600" />
                            </div>
                            <h4 className="text-sm xs:text-base font-medium text-gray-900">Achats associés</h4>
                        </div>

                        <div className="space-y-2 xs:space-y-3">
                            {appointment.purchases.map((purchase, index) => (
                                <div key={index} className={`rounded-lg p-2 xs:p-3 ${
                                    purchase.status === "purchased"
                                        ? "bg-green-50 border border-green-100"
                                        : "bg-gray-50 border border-gray-100"
                                }`}>
                                    {purchase.status === "purchased" ? (
                                        <>
                                            <div className="flex flex-col xs:flex-row xs:items-start xs:justify-between gap-2">
                                                <div className="flex-1">
                                                    <span className="block text-xs xs:text-sm font-medium text-gray-900">{purchase.item_type}</span>
                                                    <span className="text-[10px] xs:text-xs text-gray-600">{`Poids: ${purchase.weight || 0}g`}</span>
                                                </div>
                                                <div className="flex-shrink-0">
                                                    <div className="space-y-1">
                                                        {/* Mobile view - compact */}
                                                        <div className="xs:hidden flex justify-between">
                                                            <span className="text-xs text-gray-600">Achat:</span>
                                                            <span className="text-xs font-medium text-gray-900 ml-2">
                                                                {purchase.buy_price > 1000
                                                                    ? `${(purchase.buy_price / 1000).toFixed(1)}k €`
                                                                    : `${purchase.buy_price || 0} €`}
                                                            </span>
                                                        </div>
                                                        <div className="xs:hidden flex justify-between">
                                                            <span className="text-xs text-gray-600">Revente:</span>
                                                            <span className="text-xs font-bold text-green-600 ml-2">
                                                                {purchase.resale_price > 1000
                                                                    ? `${(purchase.resale_price / 1000).toFixed(1)}k €`
                                                                    : `${purchase.resale_price || 0} €`}
                                                            </span>
                                                        </div>

                                                        {/* Desktop view - full */}
                                                        <div className="hidden xs:block text-right">
                                                            <div className="font-bold text-green-600 text-xs sm:text-sm">
                                                                Revente: {purchase.resale_price?.toLocaleString() || '0'} €
                                                            </div>
                                                            <div className="text-gray-600 text-xs sm:text-sm">
                                                                Achat: {purchase.buy_price?.toLocaleString() || '0'} €
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {purchase.description && (
                                                <div className="mt-2 flex items-start">
                                                    <div className="bg-white p-1 rounded-full flex-shrink-0 mt-0.5">
                                                        <Info className="h-3 w-3 text-gray-400" />
                                                    </div>
                                                    <p className="ml-2 text-[10px] xs:text-xs text-gray-600 line-clamp-2">
                                                        {purchase.description}
                                                    </p>
                                                </div>
                                            )}
                                        </>
                                    ) : (
                                        <div className="flex items-center">
                                            <div className="bg-gray-200 p-1 rounded-full flex-shrink-0">
                                                <AlertCircle className="h-3 w-3 xs:h-4 xs:w-4 text-gray-500" />
                                            </div>
                                            <div className="ml-2">
                                                <span className="block text-xs xs:text-sm font-medium text-gray-400 line-through">
                                                    {purchase.item_type}
                                                </span>
                                                <span className="text-[10px] xs:text-xs text-gray-500">
                                                    (Element non acheté)
                                                </span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                )}
                {/* 
                <div className="flex flex-col justify-end space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                    <Button size="sm" variant="outline" className="w-full sm:w-auto">
                        <Eye className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                        Voir
                    </Button>
                    <Button size="sm" variant="outline" className="w-full sm:w-auto">
                        <Edit className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                        Modifier
                    </Button>
                    <Button size="sm" variant="outline" className="w-full text-red-600 hover:text-red-700 sm:w-auto">
                        <Trash2 className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                        Supprimer
                    </Button>
                </div> */}
            </CardContent>
        </Card>
    );

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Base de données RDV" />
            <div className="space-y-3 xs:space-y-4 sm:space-y-6 p-3 xs:p-4 sm:p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">
                            Base de Données des Rendez-vous
                        </h1>
                        <p className="mt-1 sm:mt-2 text-xs xs:text-sm sm:text-base text-gray-600 leading-relaxed">
                            Gérez tous les rendez-vous avec filtrage et export
                        </p>
                    </div>
                    <Dialog>
                        <DialogTrigger asChild>
                            <Button className="w-full sm:w-auto min-h-[44px] text-sm xs:text-base border-2 hover:border-blue-500 transition-all duration-200">
                                <Download className="mr-2 h-4 w-4 flex-shrink-0" />
                                <span className="xs:hidden">Export</span>
                                <span className="hidden xs:inline">Exporter</span>
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="w-[95vw] max-w-md mx-auto">
                            <DialogHeader className="pb-3 border-b">
                                <DialogTitle className="text-base xs:text-lg">Exporter les rendez-vous</DialogTitle>
                                <DialogDescription className="text-xs xs:text-sm">
                                    Choisissez le mois et l'année à exporter.
                                </DialogDescription>
                            </DialogHeader>
                            <div className="flex flex-col gap-3 xs:gap-4 mt-4">
                                <div>
                                    <label className="block mb-2 text-xs xs:text-sm font-medium text-gray-700">Mois</label>
                                    <select
                                        value={exportMonth}
                                        onChange={e => setExportMonth(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2 xs:p-3 text-sm min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Sélectionnez un mois</option>
                                        {[...Array(12)].map((_, i) => (
                                            <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                                                {new Date(0, i).toLocaleString('fr-FR', { month: 'long' })}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block mb-2 text-xs xs:text-sm font-medium text-gray-700">Année</label>
                                    <input
                                        type="number"
                                        min="2000"
                                        max={new Date().getFullYear()}
                                        value={exportYear}
                                        onChange={e => setExportYear(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2 xs:p-3 text-sm min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="ex: 2025"
                                    />
                                </div>
                                <Button
                                    className="w-full min-h-[44px] text-sm xs:text-base mt-2"
                                    onClick={handleExport}
                                    disabled={!exportMonth || !exportYear}
                                >
                                    <Download className="mr-2 h-4 w-4 flex-shrink-0" />
                                    Exporter
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>

                {/* Search and Filters */}
                <Card className="border-0 bg-white shadow-sm">
                    <CardContent className="p-3 xs:p-4 sm:p-6">
                        <div className="flex flex-col gap-3 sm:gap-4 lg:flex-row">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="Rechercher par nom, téléphone ou adresse..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 py-2 xs:py-2.5 sm:py-3 pr-4 pl-10 text-xs xs:text-sm focus:border-transparent focus:ring-2 focus:ring-blue-500 min-h-[44px]"
                                        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                    />
                                </div>
                            </div>
                            <div className="flex flex-col xs:flex-row gap-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setShowFilters(!showFilters)}
                                    className="w-full xs:w-auto min-h-[44px] text-xs xs:text-sm border-2 hover:border-blue-500 transition-all duration-200"
                                >
                                    <Filter className="mr-2 h-4 w-4 flex-shrink-0" />
                                    <span className="xs:hidden">Filtres ({Object.values(filters || {}).filter(Boolean).length})</span>
                                    <span className="hidden xs:inline">Filtres</span>
                                </Button>
                                <Button
                                    onClick={handleSearch}
                                    className="w-full xs:w-auto min-h-[44px] text-xs xs:text-sm"
                                >
                                    <Search className="mr-2 h-4 w-4 flex-shrink-0 xs:hidden" />
                                    Rechercher
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={clearFilters}
                                    className="w-full xs:w-auto min-h-[44px] text-xs xs:text-sm"
                                >
                                    Effacer
                                </Button>
                            </div>
                        </div>

                        {showFilters && (
                            <div className="mt-3 xs:mt-4 grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 xs:gap-4 border-t border-gray-100 pt-3 xs:pt-4 bg-gray-50 -mx-3 xs:-mx-4 sm:-mx-6 px-3 xs:px-4 sm:px-6 pb-3 xs:pb-4 rounded-b-lg">
                                <div>
                                    <label className="mb-1 xs:mb-2 block text-xs xs:text-sm font-medium text-gray-700">Date début</label>
                                    <input
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2 xs:p-2.5 text-xs xs:text-sm min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                                <div>
                                    <label className="mb-1 xs:mb-2 block text-xs xs:text-sm font-medium text-gray-700">Date fin</label>
                                    <input
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2 xs:p-2.5 text-xs xs:text-sm min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                                <div>
                                    <label className="mb-1 xs:mb-2 block text-xs xs:text-sm font-medium text-gray-700">Source</label>
                                    <select
                                        value={selectedSource}
                                        onChange={(e) => setSelectedSource(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2 xs:p-2.5 text-xs xs:text-sm min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Toutes les sources</option>
                                        <option value="leboncoin">LBC</option>
                                        <option value="outbound">APPEL</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="mb-1 xs:mb-2 block text-xs xs:text-sm font-medium text-gray-700">Assistant</label>
                                    <select
                                        value={selectedAssistant}
                                        onChange={(e) => setSelectedAssistant(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2 xs:p-2.5 text-xs xs:text-sm min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Tous les assistants</option>
                                        {assistants?.map((assistant) => (
                                            <option key={assistant.id} value={assistant.id}>
                                                {assistant.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="mb-1 xs:mb-2 block text-xs xs:text-sm font-medium text-gray-700">Représentant</label>
                                    <select
                                        value={selectedRepresentative}
                                        onChange={(e) => setSelectedRepresentative(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2 xs:p-2.5 text-xs xs:text-sm min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Tous les représentants</option>
                                        {representatives?.map((rep) => (
                                            <option key={rep.id} value={rep.id}>
                                                {rep.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Appointments List */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 xs:gap-4 sm:gap-5 lg:gap-6">
                    {appointments?.data?.map((appointment) => (
                        <AppointmentCard key={appointment.id} appointment={appointment} />
                    ))}
                </div>

                {/* Pagination */}
                {appointments?.links && (
                    <div className="bg-white rounded-lg border border-gray-100 p-3 xs:p-4">
                        <div className="flex flex-col xs:flex-row items-center justify-between gap-3">
                            {/* Results info */}
                            <div className="text-xs xs:text-sm text-gray-600 text-center xs:text-left">
                                Affichage de <span className="font-medium">{appointments.from || 0}</span> à{' '}
                                <span className="font-medium">{appointments.to || 0}</span> sur{' '}
                                <span className="font-medium">{appointments.total || 0}</span> résultats
                            </div>

                            {/* Pagination controls */}
                            <div className="flex items-center gap-1 xs:gap-2">
                                {/* Previous button */}
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        const prevPageUrl = appointments.links.find(link => link.label === "&laquo; Previous")?.url;
                                        prevPageUrl && router.get(prevPageUrl);
                                    }}
                                    disabled={!appointments.links.find(link => link.label === "&laquo; Previous")?.url}
                                    className="px-2 xs:px-3 py-1 text-xs xs:text-sm min-h-[36px]"
                                >
                                    <ChevronLeft className="h-4 w-4 mr-1" />
                                    <span className="hidden xs:inline">Précédent</span>
                                </Button>

                                {/* Mobile: Show only current page info */}
                                <div className="xs:hidden bg-white border border-gray-200 rounded px-2 py-1 text-xs font-medium">
                                    {appointments.current_page} / {appointments.last_page}
                                </div>

                                {/* Desktop: Show page numbers */}
                                <div className="hidden xs:flex items-center space-x-1">
                                    {appointments.links.filter(link =>
                                        !["&laquo; Previous", "&raquo; Next"].includes(link.label)
                                    ).map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={link.active ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => link.url && router.get(link.url)}
                                            disabled={!link.url}
                                            className="px-3 py-1 text-xs min-h-[36px]"
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>

                                {/* Next button */}
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        const nextPageUrl = appointments.links.find(link => link.label === "&raquo; Next")?.url;
                                        nextPageUrl && router.get(nextPageUrl);
                                    }}
                                    disabled={!appointments.links.find(link => link.label === "&raquo; Next")?.url}
                                    className="px-2 xs:px-3 py-1 text-xs xs:text-sm min-h-[36px]"
                                >
                                    <span className="hidden xs:inline">Suivant</span>
                                    <ChevronRight className="h-4 w-4 ml-1" />
                                </Button>
                            </div>
                        </div>
                    </div>
                )}

                {(!appointments?.data || appointments.data.length === 0) && (
                    <div className="bg-gray-50 rounded-xl border border-gray-100 py-8 xs:py-10 sm:py-12 text-center">
                        <div className="bg-white p-3 rounded-full w-14 h-14 xs:w-16 xs:h-16 mx-auto mb-4 shadow-sm flex items-center justify-center">
                            <Calendar className="h-8 w-8 text-gray-300" />
                        </div>
                        <h3 className="text-base xs:text-lg font-medium text-gray-900 mb-2">Aucun rendez-vous trouvé</h3>
                        <p className="text-xs xs:text-sm text-gray-500 max-w-md mx-auto">
                            Essayez de modifier vos critères de recherche ou d'effacer les filtres
                        </p>
                        <Button
                            variant="outline"
                            onClick={clearFilters}
                            className="mt-4 min-h-[44px] text-xs xs:text-sm"
                        >
                            Effacer les filtres
                        </Button>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
