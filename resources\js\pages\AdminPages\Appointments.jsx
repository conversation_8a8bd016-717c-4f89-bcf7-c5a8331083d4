import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm, usePage } from '@inertiajs/react';
import { Calendar, Clock, Download, Edit, Eye, Filter, MapPin, Phone, Search, Trash2, User } from 'lucide-react';
import { useState } from 'react';
const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Base de données RDV',
        href: '/admin/appointments',
    },
];

export default function AdminAppointments() {
    const { data, setData, post, processing } = useForm();
    const { appointments, assistants, representatives, filters } = usePage().props;
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [dateFrom, setDateFrom] = useState(filters?.date_from || '');
    const [dateTo, setDateTo] = useState(filters?.date_to || '');
    const [selectedSource, setSelectedSource] = useState(filters?.source || '');
    const [selectedAssistant, setSelectedAssistant] = useState(filters?.assistant_id || '');
    const [selectedRepresentative, setSelectedRepresentative] = useState(filters?.representative_id || '');
    const [showFilters, setShowFilters] = useState(false);
    const [exportMonth, setExportMonth] = useState('');
    const [exportYear, setExportYear] = useState('');

    console.log(appointments && appointments);


    const handleSearch = () => {
        router.get(
            '/admin/appointments',
            {
                search: searchTerm,
                date_from: dateFrom,
                date_to: dateTo,
                source: selectedSource,
                assistant_id: selectedAssistant,
                representative_id: selectedRepresentative,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const handleExport = () => {
        if (!exportMonth || !exportYear) return;
        window.location.href = `/admin/appointments/export?month=${exportMonth}&year=${exportYear}`;
    };

    const clearFilters = () => {
        setSearchTerm('');
        setDateFrom('');
        setDateTo('');
        setSelectedSource('');
        setSelectedAssistant('');
        setSelectedRepresentative('');
        router.get('/admin/appointments');
    };

    const getSourceBadge = (source) => {
        const sourceConfig = {
            leboncoin: { label: 'LBC', className: 'bg-blue-50 text-blue-700' },
            outbound: { label: 'APPEL', className: 'bg-green-50 text-green-700' },
        };

        const config = sourceConfig[source] || { label: source, className: 'bg-gray-50 text-gray-700' };

        return (
            <Badge variant="outline" className={config.className}>
                {config.label}
            </Badge>
        );
    };

    const getStatusBadge = (appointment) => {
        const hasPurchase = appointment.purchases && appointment.purchases.length > 0;
        // const purchaseStatus = hasPurchase ? appointment.purchases[0].status : null;
        // console.log(appointment);

        if (appointment.appointment_type === 'announced') {
            return <Badge className="bg-green-50 text-green-700">Annoncé (d'après SA)</Badge>;
        } else {
            return <Badge className="bg-red-50 text-red-700">Non Annoncé</Badge>;
        }
    };

    const AppointmentCard = ({ appointment }) => (
        <Card className="border-0 bg-white shadow-sm transition-shadow hover:shadow-md">
            <CardContent className="p-4 sm:p-6">
                <div className="mb-4 flex flex-col items-start justify-between space-y-3 sm:flex-row sm:space-y-0">
                    <div className="flex min-w-0 flex-1 items-center space-x-3">
                        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 sm:h-12 sm:w-12">
                            <Calendar className="h-5 w-5 text-blue-600 sm:h-6 sm:w-6" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="truncate text-sm font-semibold text-gray-900 sm:text-base">{appointment.clientName}</h3>
                            <p className="mt-1 flex items-center text-xs text-gray-500 sm:text-sm">
                                <Clock className="mr-1 h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                                <span className="truncate">{new Date(appointment.dateTime).toLocaleString('fr-FR')}</span>
                            </p>
                        </div>
                    </div>
                    <div className="flex flex-shrink-0 items-center space-x-2">
                        {getSourceBadge(appointment.source)}
                        {getStatusBadge(appointment)}
                    </div>
                </div>

                <div className="mb-4 grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
                    <div className="space-y-2">
                        <div className="flex items-center text-xs text-gray-600 sm:text-sm">
                            <Phone className="mr-2 h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                            <span className="truncate">{appointment.clientPhone}</span>
                        </div>
                        <div className="flex items-center text-xs text-gray-600 sm:text-sm">
                            <MapPin className="mr-2 h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                            <span className="truncate">{appointment.clientAddress}</span>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <div className="flex items-center text-xs text-gray-600 sm:text-sm">
                            <User className="mr-2 h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                            <span className="truncate">SA: {appointment.assistant?.name}</span>
                        </div>
                        <div className="flex items-center text-xs text-gray-600 sm:text-sm">
                            <User className="mr-2 h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                            <span className="truncate">SR: {appointment.representative?.name}</span>
                        </div>
                    </div>
                </div>

                {appointment.notes && (
                    <div className="mb-4">
                        <p className="rounded-lg bg-gray-50 p-2 text-xs text-gray-600 sm:p-3 sm:text-sm">
                            <strong>Notes:</strong> {appointment.notes}
                        </p>
                    </div>
                )}

                {appointment.purchases && appointment.purchases.length > 0 && (
                    <div className="mb-4">
                        <h4 className="mb-2 text-sm font-medium text-gray-900 sm:text-base">Achats associés:</h4>
                        <div className="space-y-2">
                            {appointment.purchases.map((purchase, index) => (
                                <div key={index} className="rounded-lg bg-gray-50 p-2 sm:p-3">
                                    {
                                        purchase.status == "purchased" ?
                                            <>
                                                <div className="flex flex-col space-y-1 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
                                                    <div className="flex-1 lg:w-[50%]">
                                                        <span className="block truncate text-xs font-medium sm:text-sm">{purchase.item_type}</span>
                                                        <span className="text-xs text-gray-500">{`Poids: ${purchase.weight || 0}g`}</span>
                                                    </div>
                                                    <div className="flex-shrink-0 text-right lg:w-[50%]">
                                                        <div className="space-y-1 text-xs sm:text-sm">
                                                            <div className="font-bold text-green-600">
                                                                Revente: {purchase.resale_price?.toLocaleString() || '0'} €
                                                            </div>
                                                            <div className="text-gray-600">Achat: {purchase.buy_price?.toLocaleString() || 'Achat: 0'} €</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {purchase.description && (
                                                    <p className="mt-1 text-xs text-gray-600 sm:text-sm">description: {purchase.description}</p>
                                                )}
                                            </>
                                            :
                                            <>
                                                <div className="flex flex-col space-y-1 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
                                                    <div className="flex-1 lg:w-[50%]">
                                                        <span className="block truncate text-xs font-medium sm:text-sm line-through ">{purchase.item_type}</span>
                                                        <span className="text-xs text-gray-500">(Element non acheté)</span>
                                                    </div>
                                                    {/* <div className="flex-shrink-0 text-right lg:w-[50%]">
                                                        <div className="space-y-1 text-xs sm:text-sm">
                                                            <div className="font-bold text-green-600">
                                                                Revente: {purchase.resale_price?.toLocaleString() || '0'} €
                                                            </div>
                                                            <div className="text-gray-600">Achat: {purchase.buy_price?.toLocaleString() || 'Achat: 0'} €</div>
                                                        </div>
                                                    </div> */}
                                                </div>
                                                {/* {purchase.description && (
                                                    <p className="mt-1 text-xs text-gray-600 sm:text-sm">description: {purchase.description}</p>
                                                )} */}
                                            </>
                                    }

                                </div>
                            ))}
                        </div>
                    </div>
                )}
                {/* 
                <div className="flex flex-col justify-end space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                    <Button size="sm" variant="outline" className="w-full sm:w-auto">
                        <Eye className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                        Voir
                    </Button>
                    <Button size="sm" variant="outline" className="w-full sm:w-auto">
                        <Edit className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                        Modifier
                    </Button>
                    <Button size="sm" variant="outline" className="w-full text-red-600 hover:text-red-700 sm:w-auto">
                        <Trash2 className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                        Supprimer
                    </Button>
                </div> */}
            </CardContent>
        </Card>
    );

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Base de données RDV" />
            <div className="space-y-4 p-4 sm:space-y-6 sm:p-6">
                {/* Header */}
                <div className="flex flex-col items-start justify-between space-y-3 sm:flex-row sm:items-center sm:space-y-0">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl lg:text-3xl">Base de Données des Rendez-vous</h1>
                        <p className="mt-1 text-sm text-gray-600 sm:text-base">Gérez tous les rendez-vous avec filtrage et export</p>
                    </div>
                    <Dialog>
                        <DialogTrigger>
                            <Button className="w-full sm:w-auto">
                                <Download className="mr-2 h-4 w-4" />
                                Exporter
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Exporter les rendez-vous</DialogTitle>
                                <DialogDescription>
                                    Choisissez le mois et l'année à exporter.
                                </DialogDescription>
                            </DialogHeader>
                            <div className="flex flex-col gap-4 mt-4">
                                <div>
                                    <label className="block mb-1 text-sm font-medium text-gray-700">Mois</label>
                                    <select
                                        value={exportMonth}
                                        onChange={e => setExportMonth(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    >
                                        <option value="">Sélectionnez un mois</option>
                                        {[...Array(12)].map((_, i) => (
                                            <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                                                {new Date(0, i).toLocaleString('fr-FR', { month: 'long' })}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block mb-1 text-sm font-medium text-gray-700">Année</label>
                                    <input
                                        type="number"
                                        min="2000"
                                        max={new Date().getFullYear()}
                                        value={exportYear}
                                        onChange={e => setExportYear(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                        placeholder="ex: 2025"
                                    />
                                </div>
                                <Button
                                    className="w-full"
                                    onClick={handleExport}
                                    disabled={!exportMonth || !exportYear}
                                >
                                    <Download className="mr-2 h-4 w-4" />
                                    Exporter
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>

                {/* Search and Filters */}
                <Card className="border-0 bg-white shadow-sm">
                    <CardContent className="p-4 sm:p-6">
                        <div className="flex flex-col gap-3 sm:gap-4 lg:flex-row">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="Rechercher par nom, téléphone ou adresse..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 py-2 pr-4 pl-10 text-sm focus:border-transparent focus:ring-2 focus:ring-blue-500 sm:py-3"
                                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                    />
                                </div>
                            </div>
                            <div className="flex flex-col gap-2 sm:flex-row">
                                <Button variant="outline" onClick={() => setShowFilters(!showFilters)} className="w-full sm:w-auto">
                                    <Filter className="mr-2 h-4 w-4" />
                                    Filtres
                                </Button>
                                <Button onClick={handleSearch} className="w-full sm:w-auto">
                                    Rechercher
                                </Button>
                                <Button variant="outline" onClick={clearFilters} className="w-full sm:w-auto">
                                    Effacer
                                </Button>
                            </div>
                        </div>

                        {showFilters && (
                            <div className="mt-4 grid grid-cols-1 gap-4 border-t pt-4 md:grid-cols-2 lg:grid-cols-5">
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Date début</label>
                                    <input
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    />
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Date fin</label>
                                    <input
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    />
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Source</label>
                                    <select
                                        value={selectedSource}
                                        onChange={(e) => setSelectedSource(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    >
                                        <option value="">Toutes les sources</option>
                                        <option value="leboncoin">LBC</option>
                                        <option value="outbound">APPEL</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Assistant</label>
                                    <select
                                        value={selectedAssistant}
                                        onChange={(e) => setSelectedAssistant(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    >
                                        <option value="">Tous les assistants</option>
                                        {assistants?.map((assistant) => (
                                            <option key={assistant.id} value={assistant.id}>
                                                {assistant.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Représentant</label>
                                    <select
                                        value={selectedRepresentative}
                                        onChange={(e) => setSelectedRepresentative(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    >
                                        <option value="">Tous les représentants</option>
                                        {representatives?.map((rep) => (
                                            <option key={rep.id} value={rep.id}>
                                                {rep.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Appointments List */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    {appointments?.data?.map((appointment) => (
                        <AppointmentCard key={appointment.id} appointment={appointment} />
                    ))}
                </div>

                {/* Pagination */}
                {appointments?.links && (
                    <div className="flex justify-center space-x-2">
                        {appointments.links.map((link, index) => (
                            <Button
                                key={index}
                                variant={link.active ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => link.url && router.get(link.url)}
                                disabled={!link.url}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}

                {(!appointments?.data || appointments.data.length === 0) && (
                    <div className="py-12 text-center">
                        <Calendar className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                        <h3 className="mb-2 text-lg font-medium text-gray-900">Aucun rendez-vous trouvé</h3>
                        <p className="text-gray-500">Essayez de modifier vos critères de recherche</p>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
