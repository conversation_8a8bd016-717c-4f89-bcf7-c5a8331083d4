import React, { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Users,
    Calendar,
    DollarSign,
    TrendingUp,
    TrendingDown,
    Target,
    Award,
    BarChart3,
    PieChart,
    Activity
} from 'lucide-react';

const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Tableau de bord global',
        href: '/admin/dashboard',
    },
];

export default function AdminDashboard() {
    const { saPerformance, srPerformance, companyStats, leaderboards } = usePage().props;

    const StatCard = ({ title, value, subtitle, icon: Icon, trend, color = "text-blue-600" }) => (
        <Card className="h-full border-0 bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                    <div className="min-w-0 flex-1">
                        <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">{title}</p>
                        <p className={`text-lg sm:text-2xl font-bold ${color} mt-1`}>{value}</p>
                        {subtitle && <p className="text-xs sm:text-sm text-gray-500 mt-1 truncate">{subtitle}</p>}
                    </div>
                    <div className={`p-2 sm:p-3 rounded-full bg-gray-50 flex-shrink-0 ml-3`}>
                        <Icon className={`h-5 w-5 sm:h-6 sm:w-6 ${color}`} />
                    </div>
                </div>
                {trend && (
                    <div className="mt-3 sm:mt-4 flex items-center">
                        {trend > 0 ? (
                            <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mr-1 flex-shrink-0" />
                        ) : (
                            <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4 text-red-500 mr-1 flex-shrink-0" />
                        )}
                        <span className={`text-xs sm:text-sm ${trend > 0 ? 'text-green-500' : 'text-red-500'} truncate`}>
                            {Math.abs(trend)}% vs semaine dernière
                        </span>
                    </div>
                )}
            </CardContent>
        </Card>
    );

    const LeaderboardCard = ({ title, data, type, icon: Icon }) => (
        <Card className="h-full border-0 bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-[#525e62] text-sm sm:text-base">
                    <Icon className="mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                    <span className="truncate">{title}</span>
                </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="space-y-2 sm:space-y-3">
                    {data?.slice(0, 5).map((item, index) => (
                        <div key={item.id} className="flex items-center justify-between p-2 sm:p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                                <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-white font-bold text-xs sm:text-sm flex-shrink-0 ${index === 0 ? 'bg-yellow-500' :
                                        index === 1 ? 'bg-gray-400' :
                                            index === 2 ? 'bg-amber-600' : 'bg-blue-500'
                                    }`}>
                                    {index + 1}
                                </div>
                                <div className="min-w-0 flex-1">
                                    <p className="font-medium text-gray-900 text-xs sm:text-sm truncate">{item.name}</p>
                                    <p className="text-xs text-gray-500 truncate hidden sm:block">{item.email}</p>
                                </div>
                            </div>
                            <div className="text-right flex-shrink-0 ml-2">
                                {type === 'revenue' && (
                                    <p className="font-bold text-green-600 text-xs sm:text-sm">
                                        {item.weekly_revenue? item.weekly_revenue?.toLocaleString() : "0"} €
                                    </p>
                                )}
                                {type === 'appointments' && (
                                    <p className="font-bold text-blue-600 text-xs sm:text-sm">
                                        {item?.weekly_appointments? item.weekly_appointments : "0"} RDV
                                    </p>
                                )}
                                {type === 'bonuses' && (
                                    <p className="font-bold text-purple-600 text-xs sm:text-sm">
                                        {item?.weekly_bonuses? item.weekly_bonuses?.toLocaleString() : "0"} €
                                    </p>
                                )}
                            </div>
                        </div>
                    ))}
                    {(!data || data.length === 0) && (
                        <div className="text-center py-6 text-gray-500">
                            <Icon className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                            <p className="text-sm">Aucune donnée disponible</p>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );

    const PerformanceTable = ({ title, data, type }) => (
        <Card className="h-full border-0 bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
                <CardTitle className="text-[#525e62] text-sm sm:text-base">{title}</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="overflow-x-auto">
                    <table className="w-full min-w-[600px]">
                        <thead>
                            <tr className="border-b">
                                <th className="text-left py-2 px-1 text-xs sm:text-sm font-medium">Nom</th>
                                {type === 'sa' && (
                                    <>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">RDV Jour</th>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">RDV Semaine</th>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">Validés</th>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">Taux</th>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">Bonus</th>
                                    </>
                                )}
                                {type === 'sr' && (
                                    <>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">CA Semaine</th>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">Achats</th>
                                        <th className="text-center py-2 px-1 text-xs sm:text-sm font-medium">CA Mois</th>
                                    </>
                                )}
                            </tr>
                        </thead>
                        <tbody>
                            {data?.map((item) => (
                                <tr key={item.id} className="border-b hover:bg-gray-50 transition-colors">
                                    <td className="py-2 sm:py-3 px-1">
                                        <div>
                                            <p className="font-medium text-xs sm:text-sm truncate">{item.name}</p>
                                            <p className="text-xs text-gray-500 truncate hidden sm:block">{item.email}</p>
                                        </div>
                                    </td>
                                    {type === 'sa' && (
                                        <>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <Badge variant="outline" className="text-xs">{item.daily_appointments}</Badge>
                                            </td>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <Badge variant="outline" className="text-xs">{item.weekly_appointments}</Badge>
                                            </td>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <Badge variant="outline" className="bg-green-50 text-green-700 text-xs">
                                                    {item.validated_appointments}
                                                </Badge>
                                            </td>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <Badge
                                                    variant="outline"
                                                    className={`text-xs ${item.validation_rate >= 70 ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}
                                                >
                                                    {item.validation_rate}%
                                                </Badge>
                                            </td>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <span className="font-bold text-purple-600 text-xs sm:text-sm">
                                                    {item.total_bonuses} €
                                                </span>
                                            </td>
                                        </>
                                    )}
                                    {type === 'sr' && (
                                        <>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <span className="font-bold text-green-600 text-xs sm:text-sm">
                                                    {item.weekly_revenue?.toLocaleString()} €
                                                </span>
                                            </td>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <Badge variant="outline" className="text-xs">{item.weekly_purchases}</Badge>
                                            </td>
                                            <td className="text-center py-2 sm:py-3 px-1">
                                                <span className="font-bold text-blue-600 text-xs sm:text-sm">
                                                    {item.monthly_revenue?.toLocaleString()} €
                                                </span>
                                            </td>
                                        </>
                                    )}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                    {(!data || data.length === 0) && (
                        <div className="text-center py-8 text-gray-500">
                            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                            <p className="text-sm">Aucune donnée disponible</p>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Tableau de bord Admin" />

            <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                {/* Header */}
                <div className="mb-6">
                    <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Tableau de Bord Global</h1>
                    <p className="text-sm sm:text-base text-gray-600 mt-1">Vue d'ensemble des performances et statistiques</p>
                </div>

                {/* Company Stats Overview */}
                <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                    <StatCard
                        title="CA Total"
                        value={`${companyStats?.total_revenue?.toLocaleString() || '0'} €`}
                        subtitle="Chiffre d'affaires global"
                        icon={DollarSign}
                        color="text-green-600"
                    />
                    <StatCard
                        title="CA Semaine"
                        value={`${companyStats?.weekly_revenue?.toLocaleString() || '0'} €`}
                        subtitle={`${companyStats?.weekly_purchases || 0} achats`}
                        icon={TrendingUp}
                        color="text-blue-600"
                    />
                    <StatCard
                        title="CA Mois"
                        value={`${companyStats?.monthly_revenue?.toLocaleString() || '0'} €`}
                        subtitle="Chiffre d'affaires mensuel"
                        icon={BarChart3}
                        color="text-purple-600"
                    />
                    <StatCard
                        title="CA Net Semaine (-5000€)"
                        value={`${companyStats?.weeklyCharges?.toLocaleString() || '0'} €`}
                        subtitle={companyStats?.profit_loss === 'profit' ? 'Bénéfice' : 'Perte'}
                        icon={companyStats?.profit_loss === 'profit' ? TrendingUp : TrendingDown}
                        color={companyStats?.profit_loss === 'profit' ? 'text-green-600' : 'text-red-600'}
                    />
                </div>

                {/* Real-time Leaderboards */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                    <LeaderboardCard
                        title="Top CA Représentants (Cette Semaine)"
                        data={leaderboards?.revenue}
                        type="revenue"
                        icon={Award}
                    />
                    <LeaderboardCard
                        title="Top RDV Assistants (Cette Semaine)"
                        data={leaderboards?.appointments}
                        type="appointments"
                        icon={Calendar}
                    />
                    <LeaderboardCard
                        title="Top Bonus Assistants (Cette Semaine)"
                        data={leaderboards?.bonuses}
                        type="bonuses"
                        icon={Target}
                    />
                </div>

                {/* Performance Tables */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                    <PerformanceTable
                        title="Performance Assistants Commerciaux"
                        data={saPerformance}
                        type="sa"
                    />
                    <PerformanceTable
                        title="Performance Représentants Commerciaux"
                        data={srPerformance}
                        type="sr"
                    />
                </div>
            </div>
        </AppLayout>
    );
}
