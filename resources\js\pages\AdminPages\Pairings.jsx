import React, { useState, useRef, useCallback, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Users,
    Calendar,
    TrendingUp,
    Award,
    History
} from 'lucide-react';

const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Système de pairing',
        href: '/admin/pairings',
    },
];

export default function AdminPairings() {
    const {
        currentWeekPairings,
        pairingHistory,
        topPerformingPairs,
        availableRepresentatives
    } = usePage().props;


    const [showAssignmentModal, setShowAssignmentModal] = useState(false);
    const [assignments, setAssignments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(pairingHistory?.current_page || 1);

    // Ref to track modal scroll position
    const modalScrollRef = useRef(null);
    const scrollPositionRef = useRef(0);

    // Effect to maintain scroll position during re-renders
    useEffect(() => {
        if (modalScrollRef.current && scrollPositionRef.current > 0) {
            modalScrollRef.current.scrollTop = scrollPositionRef.current;
        }
    }, [assignments]);

    // Effect to track scroll position continuously
    useEffect(() => {
        const modalElement = modalScrollRef.current;
        if (!modalElement) return;

        const handleScroll = () => {
            scrollPositionRef.current = modalElement.scrollTop;
        };

        modalElement.addEventListener('scroll', handleScroll, { passive: true });

        return () => {
            modalElement.removeEventListener('scroll', handleScroll);
        };
    }, [showAssignmentModal]);



    const loadAssignments = async () => {
        setLoading(true);
        try {
            const response = await fetch('/admin/assignments', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Loaded assignment data:', data); // Debug log

            // Initialize assignments with current data
            const initialAssignments = data.assistants.map(assistant => ({
                assistant_id: assistant.id,
                assistant_name: assistant.name,
                representative_ids: assistant.assigned_representatives ? assistant.assigned_representatives.map(rep => rep.id) : []
            }));

            console.log('Processed assignments:', initialAssignments); // Debug log
            setAssignments(initialAssignments);
        } catch (error) {
            console.error('Error loading assignments:', error);
            alert('Erreur lors du chargement des assignations: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleOpenAssignmentModal = (e) => {
        e?.preventDefault();
        e?.stopPropagation();
        console.log('Opening assignment modal...'); // Debug log
        setShowAssignmentModal(true);
        loadAssignments();
    };

    const handleSaveAssignments = () => {
        console.log('Saving assignments:', assignments); // Debug log
        setLoading(true);

        router.post('/admin/assignments', { assignments }, {
            onSuccess: () => {
                console.log('Assignments saved successfully');
                setShowAssignmentModal(false);
                setLoading(false);
                // Page will automatically refresh with updated data
            },
            onError: (errors) => {
                console.error('Save failed:', errors);
                setLoading(false);
                alert('Erreur lors de la sauvegarde des assignations: ' + (errors.message || 'Erreur inconnue'));
            },
            onFinish: () => {
                setLoading(false);
            }
        });
    };

    const updateAssignment = useCallback((assistantId, representativeIds, event) => {
        // Store current scroll position in ref
        if (modalScrollRef.current) {
            scrollPositionRef.current = modalScrollRef.current.scrollTop;
        }

        // Prevent default behavior and stop propagation
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        console.log(`Updating assignment for assistant ${assistantId}:`, representativeIds); // Debug log

        // Update assignments state
        setAssignments(prev => {
            const updated = prev.map(assignment =>
                assignment.assistant_id === assistantId
                    ? { ...assignment, representative_ids: representativeIds }
                    : assignment
            );
            console.log('Updated assignments state:', updated); // Debug log
            return updated;
        });

        // Restore scroll position after state update (double protection)
        requestAnimationFrame(() => {
            if (modalScrollRef.current) {
                modalScrollRef.current.scrollTop = scrollPositionRef.current;
            }
        });
    }, []);

    const handlePageChange = (page) => {
        if (page < 1 || page > pairingHistory?.last_page) return;

        setCurrentPage(page);
        router.get('/admin/pairings', { page }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const PairingCard = ({ pairing }) => (
        <Card className="border-0 bg-white shadow-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02]">
            <CardContent className="p-3 xs:p-4 sm:p-5 md:p-6">
                {/* Header Section */}
                <div className="flex flex-col space-y-3 mb-4 sm:mb-5">
                    <div className="flex items-center space-x-3 min-w-0">
                        <div className="w-10 h-10 xs:w-11 xs:h-11 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <Users className="h-4 w-4 xs:h-5 xs:w-5 sm:h-6 sm:w-6 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-gray-900 text-sm xs:text-base sm:text-lg leading-tight">
                                <span className="block xs:hidden">
                                    {pairing.assistant_name}
                                    <br />
                                    <span className="text-blue-600">↔</span> {pairing.representative_name}
                                </span>
                                <span className="hidden xs:block">
                                    {pairing.assistant_name} <span className="text-blue-600">↔</span> {pairing.representative_name}
                                </span>
                            </h3>
                            <p className="text-xs xs:text-sm text-gray-500 mt-1">Pairing actuel</p>
                        </div>
                    </div>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 xs:gap-3 sm:gap-4">
                    <div className="text-center p-2 xs:p-3 sm:p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                        <p className="text-base xs:text-lg sm:text-xl md:text-2xl font-bold text-blue-600 leading-tight">
                            {pairing.total_appointments || 0}
                        </p>
                        <p className="text-xs xs:text-sm text-gray-600 mt-1 leading-tight">RDV totales</p>
                    </div>
                    <div className="text-center p-2 xs:p-3 sm:p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                        <p className="text-base xs:text-lg sm:text-xl md:text-2xl font-bold text-green-600 leading-tight">
                            {pairing.successful_appointments || 0}
                        </p>
                        <p className="text-xs xs:text-sm text-gray-600 mt-1 leading-tight">RDV Réussis</p>
                    </div>
                    <div className="text-center p-2 xs:p-3 sm:p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                        <p className="text-base xs:text-lg sm:text-xl md:text-2xl font-bold text-purple-600 leading-tight">
                            {pairing.success_rate || 0}%
                        </p>
                        <p className="text-xs xs:text-sm text-gray-600 mt-1 leading-tight">Taux Succès</p>
                    </div>
                    <div className="text-center p-2 xs:p-3 sm:p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                        <p className="text-base xs:text-lg sm:text-xl md:text-2xl font-bold text-orange-600 leading-tight">
                            <span className="block xs:hidden text-sm">
                                {pairing.total_revenue > 1000
                                    ? `${(pairing.total_revenue / 1000).toFixed(1)}k €`
                                    : `${pairing.total_revenue || 0} €`
                                }
                            </span>
                            <span className="hidden xs:block">
                                {pairing.total_revenue?.toLocaleString() || '0'} €
                            </span>
                        </p>
                        <p className="text-xs xs:text-sm text-gray-600 mt-1 leading-tight">CA Généré</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const TopPairCard = ({ pair, rank }) => {
        // Get badge color based on rank
        const getBadgeColor = () => {
            switch (rank) {
                case 1: return 'bg-yellow-500 shadow-yellow-200 shadow-sm';
                case 2: return 'bg-gray-400';
                case 3: return 'bg-amber-600';
                default: return 'bg-blue-500';
            }
        };

        return (
            <div className="flex items-center justify-between p-3 xs:p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-all duration-200 hover:scale-[1.01]">
                <div className="flex items-center space-x-2 xs:space-x-3 min-w-0 flex-1">
                    <div className={`w-7 h-7 xs:w-8 xs:h-8 sm:w-9 sm:h-9 rounded-full flex items-center justify-center text-white font-bold text-xs xs:text-sm flex-shrink-0 ${getBadgeColor()}`}>
                        {rank}
                    </div>
                    <div className="min-w-0 flex-1">
                        {/* Mobile view - stacked names */}
                        <div className="xs:hidden">
                            <p className="font-medium text-gray-900 text-xs leading-tight">
                                {pair.assistant_name}<span className='text-[9px] ml-0.5'>(Assistant)</span>
                            </p>
                            <p className="font-medium text-gray-900 text-xs leading-tight flex items-center">
                                <span className="text-blue-600 mr-1">↔</span>
                                {pair.representative_name} <span className='text-[9px] ml-0.5'>(Commercial)</span>
                            </p>
                        </div>

                        {/* Desktop view - inline names */}
                        <p className="hidden xs:block font-medium text-gray-900 text-xs sm:text-sm truncate">
                            {pair.assistant_name} <span className="text-blue-600">↔</span> {pair.representative_name}
                        </p>

                        <p className="text-xs text-gray-500 truncate mt-1">
                            <span className="inline-flex items-center">
                                <span className="font-medium text-blue-600">{pair.total_appointments}</span>
                                <span className="mx-1">•</span>
                                <span className="font-medium text-green-600">{pair.successful_appointments}</span> réussis
                            </span>
                        </p>
                    </div>
                </div>
                <div className="text-right flex-shrink-0 ml-2">
                    <p className="font-bold text-green-600 text-sm xs:text-base">{pair.success_rate || 0}%</p>
                    <p className="text-xs text-gray-500">
                        <span className="xs:hidden">
                            {pair.total_revenue > 1000
                                ? `${(pair.total_revenue / 1000).toFixed(1)}k €`
                                : `${pair.total_revenue || 0} €`
                            }
                        </span>
                        <span className="hidden xs:inline">
                            {pair.total_revenue?.toLocaleString() || '0'} €
                        </span>
                    </p>
                </div>
            </div>
        );
    };



    const AssignmentModal = () => (
        showAssignmentModal && (
            <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-2 xs:p-3 sm:p-4 backdrop-blur-sm">
                <div
                    ref={modalScrollRef}
                    className="bg-white rounded-xl p-3 xs:p-4 sm:p-6 w-full max-w-4xl max-h-[95vh] overflow-y-auto shadow-xl"
                >
                    {/* Modal Header */}
                    <div className="flex justify-between items-center mb-4 sm:mb-6 sticky top-0 bg-white z-10 pb-2 border-b">
                        <h3 className="text-base xs:text-lg sm:text-xl font-semibold text-gray-900">
                            Gérer les Assignations SA ↔ SR
                        </h3>
                        <button
                            onClick={() => setShowAssignmentModal(false)}
                            className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-colors"
                            aria-label="Fermer"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                        </button>
                    </div>

                    <div className="space-y-4 sm:space-y-6">
                        <p className="text-xs xs:text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-100">
                            Assignez des représentants commerciaux à chaque assistant. Les assistants ne pourront choisir que parmi leurs représentants assignés lors de la création de rendez-vous.
                        </p>

                        {loading ? (
                            <div className="text-center py-8 sm:py-12">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                                <p className="mt-3 text-gray-600">Chargement...</p>
                            </div>
                        ) : assignments.length === 0 ? (
                            <div className="text-center py-8 sm:py-12 px-4">
                                <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Users className="h-8 w-8 text-gray-400" />
                                </div>
                                <p className="text-gray-700 font-medium">Aucune donnée d'assignation trouvée</p>
                                <p className="text-xs xs:text-sm text-gray-500 mt-2 mb-4">Vérifiez que des assistants existent dans le système</p>
                                <Button
                                    onClick={loadAssignments}
                                    className="bg-blue-600 hover:bg-blue-700 text-white"
                                >
                                    Recharger
                                </Button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {assignments.map((assignment) => (
                                    <div key={assignment.assistant_id} className="border rounded-lg p-3 xs:p-4 bg-gray-50 hover:bg-gray-100/80 transition-colors">
                                        <div className="flex flex-col xs:flex-row xs:items-center justify-between gap-2 mb-3 pb-2 border-b border-gray-200">
                                            <h4 className="font-medium text-gray-900 text-sm xs:text-base">
                                                {assignment.assistant_name}
                                            </h4>
                                            <span className="text-xs bg-white px-2 py-1 rounded-full border border-gray-200 inline-flex items-center justify-center w-fit">
                                                <span className="font-medium text-blue-600 mr-1">{assignment.representative_ids.length}</span> assigné(s)
                                            </span>
                                        </div>
                                        <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-2">
                                            {availableRepresentatives?.map((rep) => (
                                                <label
                                                    key={rep.id}
                                                    className="flex items-center space-x-2 p-2 xs:p-3 rounded-lg bg-white hover:bg-blue-50 transition-colors cursor-pointer border border-transparent hover:border-blue-100 min-h-[44px]"
                                                    onClick={(e) => {
                                                        // Prevent label click from interfering with checkbox
                                                        e.preventDefault();
                                                    }}
                                                >
                                                    <input
                                                        type="checkbox"
                                                        checked={assignment.representative_ids.includes(rep.id)}
                                                        onChange={(e) => {
                                                            const newIds = e.target.checked
                                                                ? [...assignment.representative_ids, rep.id]
                                                                : assignment.representative_ids.filter(id => id !== rep.id);

                                                            // Update assignment with preserved scroll position
                                                            updateAssignment(assignment.assistant_id, newIds, e);
                                                        }}
                                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4 xs:h-5 xs:w-5"
                                                    />
                                                    <span className="text-xs xs:text-sm text-gray-700">{rep.name}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Modal Footer */}
                        <div className="flex flex-col xs:flex-row xs:justify-end gap-2 xs:gap-3 pt-4 border-t mt-4 sticky bottom-0 bg-white pb-1">
                            <Button
                                variant="outline"
                                onClick={() => setShowAssignmentModal(false)}
                                disabled={loading}
                                className="w-full xs:w-auto order-2 xs:order-1 min-h-[44px]"
                            >
                                Annuler
                            </Button>
                            <Button
                                onClick={handleSaveAssignments}
                                disabled={loading}
                                className="bg-blue-600 hover:bg-blue-700 text-white w-full xs:w-auto order-1 xs:order-2 min-h-[44px]"
                            >
                                {loading ? 'Sauvegarde...' : 'Confirmer les Assignations'}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        )
    );

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Système de Pairing" />

            <div className="space-y-4 xs:space-y-5 sm:space-y-6 p-3 xs:p-4 sm:p-6">
                {/* Header with Add Button */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">
                            Système de Pairing Hebdomadaire
                        </h1>
                        <p className="text-xs xs:text-sm sm:text-base text-gray-600 mt-1 sm:mt-2 leading-relaxed">
                            Gérez les assignations SA ↔ SR et suivez leurs performances
                        </p>
                    </div>
                    <Button
                        type="button"
                        onClick={handleOpenAssignmentModal}
                        variant="outline"
                        className="w-full sm:w-auto min-h-[44px] text-sm xs:text-base border-2 hover:border-blue-500 hover:text-blue-600 transition-all duration-200"
                    >
                        <Users className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span className="xs:hidden">Assignations</span>
                        <span className="hidden xs:inline">Gérer Assignations</span>
                    </Button>
                </div>

                {/* Current Week Pairings */}
                <div>
                    <div className="flex items-center justify-between mb-3 xs:mb-4 sm:mb-5">
                        <h2 className="text-base xs:text-lg sm:text-xl font-semibold text-gray-900 flex items-center">
                            <div className="bg-blue-100 p-1.5 rounded-full mr-2 flex-shrink-0">
                                <Calendar className="h-4 w-4 xs:h-5 xs:w-5 text-blue-600" />
                            </div>
                            <span>Pairings de la Semaine Actuelle</span>
                        </h2>

                        {currentWeekPairings?.length > 0 && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                                {currentWeekPairings.length} pairing{currentWeekPairings.length > 1 ? 's' : ''}
                            </span>
                        )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 xs:gap-4 lg:gap-6">
                        {currentWeekPairings?.map((pairing, index) => (
                            <PairingCard key={index} pairing={pairing} showActions={true} />
                        ))}
                        {(!currentWeekPairings || currentWeekPairings.length === 0) && (
                            <div className="col-span-full bg-gray-50 rounded-xl border border-gray-100 text-center py-8 sm:py-12 text-gray-500">
                                <div className="bg-white p-3 rounded-full w-16 h-16 mx-auto mb-4 shadow-sm flex items-center justify-center">
                                    <Users className="h-8 w-8 text-gray-300" />
                                </div>
                                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">Aucun pairing actif</h3>
                                <p className="text-xs xs:text-sm text-gray-500 max-w-md mx-auto">
                                    Créez votre premier pairing pour cette semaine pour commencer à suivre les performances
                                </p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Top Performing Pairs */}
                <Card className="border-0 bg-white shadow-lg overflow-hidden">
                    <CardHeader className="pb-3 xs:pb-4 border-b border-gray-100">
                        <CardTitle className="flex items-center justify-between text-gray-900">
                            <div className="flex items-center">
                                <div className="bg-yellow-100 p-1.5 rounded-full mr-2 flex-shrink-0">
                                    <Award className="h-4 w-4 xs:h-5 xs:w-5 text-yellow-600" />
                                </div>
                                <span className="text-base xs:text-lg">Top Pairings Performants</span>
                            </div>
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full font-medium">
                                30 derniers jours
                            </span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 xs:p-4 sm:p-5">
                        <div className="space-y-2 xs:space-y-3">
                            {topPerformingPairs?.map((pair, index) => (
                                <TopPairCard key={index} pair={pair} rank={index + 1} />
                            ))}
                            {(!topPerformingPairs || topPerformingPairs.length === 0) && (
                                <div className="bg-gray-50 rounded-xl border border-gray-100 text-center py-8 sm:py-10 text-gray-500">
                                    <div className="bg-white p-3 rounded-full w-14 h-14 mx-auto mb-4 shadow-sm flex items-center justify-center">
                                        <Award className="h-6 w-6 text-gray-300" />
                                    </div>
                                    <h3 className="text-base font-medium text-gray-900 mb-2">Aucune donnée disponible</h3>
                                    <p className="text-xs xs:text-sm text-gray-500 max-w-md mx-auto">
                                        Les performances des pairings s'afficheront ici une fois que des rendez-vous auront été réalisés
                                    </p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Pairing History */}
                <Card className="border-0 bg-white shadow-lg overflow-hidden">
                    <CardHeader className="pb-3 xs:pb-4 border-b border-gray-100">
                        <CardTitle className="flex items-center text-gray-900">
                            <div className="bg-purple-100 p-1.5 rounded-full mr-2 flex-shrink-0">
                                <History className="h-4 w-4 xs:h-5 xs:w-5 text-purple-600" />
                            </div>
                            <span className="text-base xs:text-lg">Historique des Pairings</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        {/* Mobile Card View */}
                        <div className="block sm:hidden">
                            {pairingHistory?.data?.map((history, index) => (
                                <div key={index} className="border-b border-gray-100 p-3 xs:p-4 hover:bg-gray-50 transition-colors">
                                    <div className="flex justify-between items-start mb-2">
                                        <div className="flex-1">
                                            <p className="font-medium text-gray-900 text-sm">{history.date}</p>
                                            <p className="text-xs text-gray-600 mt-1">
                                                {history.assistant_name} <span className="text-blue-600">↔</span> {history.representative_name}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-bold text-green-600 text-sm">
                                                {history.revenue > 1000
                                                    ? `${(history.revenue / 1000).toFixed(1)}k €`
                                                    : `${history.revenue?.toLocaleString() || 0} €`
                                                }
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <div className="flex space-x-3">
                                            <div className="text-center">
                                                <Badge variant="outline" className="text-xs">{history.appointments_count}</Badge>
                                                <p className="text-xs text-gray-500 mt-1">RDV</p>
                                            </div>
                                            <div className="text-center">
                                                <Badge variant="outline" className="bg-green-50 text-green-700 text-xs">
                                                    {history.purchases_count}
                                                </Badge>
                                                <p className="text-xs text-gray-500 mt-1">Réussis</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Desktop Table View */}
                        <div className="hidden sm:block overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50">
                                    <tr className="border-b border-gray-200">
                                        <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">Date</th>
                                        <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">Assistant</th>
                                        <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">Représentant</th>
                                        <th className="text-center py-3 px-4 font-medium text-gray-700 text-sm">RDV</th>
                                        <th className="text-center py-3 px-4 font-medium text-gray-700 text-sm">Réussis</th>
                                        <th className="text-center py-3 px-4 font-medium text-gray-700 text-sm">CA</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {pairingHistory?.data?.map((history, index) => (
                                        <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                            <td className="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{history.date}</td>
                                            <td className="py-3 px-4 text-sm text-gray-700">{history.assistant_name}</td>
                                            <td className="py-3 px-4 text-sm text-gray-700">{history.representative_name}</td>
                                            <td className="text-center py-3 px-4">
                                                <Badge variant="outline" className="text-xs">{history.appointments_count}</Badge>
                                            </td>
                                            <td className="text-center py-3 px-4">
                                                <Badge variant="outline" className="bg-green-50 text-green-700 text-xs">
                                                    {history.purchases_count}
                                                </Badge>
                                            </td>
                                            <td className="text-center py-3 px-4">
                                                <span className="font-bold text-green-600 whitespace-nowrap text-sm">
                                                    {history.revenue?.toLocaleString()} €
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {(!pairingHistory?.data || pairingHistory.data.length === 0) && (
                            <div className="bg-gray-50 text-center py-8 sm:py-12 text-gray-500">
                                <div className="bg-white p-3 rounded-full w-14 h-14 mx-auto mb-4 shadow-sm flex items-center justify-center">
                                    <History className="h-6 w-6 text-gray-300" />
                                </div>
                                <h3 className="text-base font-medium text-gray-900 mb-2">Aucun historique disponible</h3>
                                <p className="text-xs xs:text-sm text-gray-500 max-w-md mx-auto">
                                    L'historique des pairings s'affichera ici au fur et à mesure des activités
                                </p>
                            </div>
                        )}

                        {/* Pagination Controls */}
                        {pairingHistory?.total > 0 && (
                            <div className="bg-gray-50 border-t border-gray-100 p-3 xs:p-4">
                                <div className="flex flex-col xs:flex-row items-center justify-between gap-3">
                                    <div className="text-xs xs:text-sm text-gray-600 text-center xs:text-left">
                                        Affichage de <span className="font-medium">{pairingHistory.from}</span> à{' '}
                                        <span className="font-medium">{pairingHistory.to}</span> sur{' '}
                                        <span className="font-medium">{pairingHistory.total}</span> résultats
                                    </div>

                                    <div className="flex items-center gap-1 xs:gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage <= 1}
                                            className="px-2 xs:px-3 py-1 text-xs xs:text-sm min-h-[36px]"
                                        >
                                            <span className="xs:hidden">‹</span>
                                            <span className="hidden xs:inline">Précédent</span>
                                        </Button>

                                        {/* Mobile: Show only current page info */}
                                        <div className="xs:hidden bg-white border border-gray-200 rounded px-2 py-1 text-xs font-medium">
                                            {currentPage} / {pairingHistory.last_page}
                                        </div>

                                        {/* Desktop: Show page numbers */}
                                        <div className="hidden xs:flex items-center space-x-1">
                                            {Array.from({ length: Math.min(5, pairingHistory.last_page) }, (_, i) => {
                                                const page = i + 1;
                                                return (
                                                    <Button
                                                        key={page}
                                                        variant={currentPage === page ? "default" : "outline"}
                                                        size="sm"
                                                        onClick={() => handlePageChange(page)}
                                                        className="px-3 py-1 text-xs min-h-[36px]"
                                                    >
                                                        {page}
                                                    </Button>
                                                );
                                            })}
                                            {pairingHistory.last_page > 5 && (
                                                <>
                                                    <span className="px-2 text-gray-400">...</span>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handlePageChange(pairingHistory.last_page)}
                                                        className="px-3 py-1 text-xs min-h-[36px]"
                                                    >
                                                        {pairingHistory.last_page}
                                                    </Button>
                                                </>
                                            )}
                                        </div>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage >= pairingHistory.last_page}
                                            className="px-2 xs:px-3 py-1 text-xs xs:text-sm min-h-[36px]"
                                        >
                                            <span className="xs:hidden">›</span>
                                            <span className="hidden xs:inline">Suivant</span>
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            <AssignmentModal />
        </AppLayout>
    );
}
