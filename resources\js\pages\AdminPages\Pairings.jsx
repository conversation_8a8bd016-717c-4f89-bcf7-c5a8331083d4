import React, { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Users,
    Calendar,
    TrendingUp,
    Award,
    History
} from 'lucide-react';

const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Système de pairing',
        href: '/admin/pairings',
    },
];

export default function AdminPairings() {
    const {
        currentWeekPairings,
        pairingHistory,
        topPerformingPairs,
        availableRepresentatives
    } = usePage().props;


    const [showAssignmentModal, setShowAssignmentModal] = useState(false);
    const [assignments, setAssignments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(pairingHistory?.current_page || 1);



    const loadAssignments = async () => {
        setLoading(true);
        try {
            const response = await fetch('/admin/assignments', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Loaded assignment data:', data); // Debug log

            // Initialize assignments with current data
            const initialAssignments = data.assistants.map(assistant => ({
                assistant_id: assistant.id,
                assistant_name: assistant.name,
                representative_ids: assistant.assigned_representatives ? assistant.assigned_representatives.map(rep => rep.id) : []
            }));

            console.log('Processed assignments:', initialAssignments); // Debug log
            setAssignments(initialAssignments);
        } catch (error) {
            console.error('Error loading assignments:', error);
            alert('Erreur lors du chargement des assignations: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleOpenAssignmentModal = (e) => {
        e?.preventDefault();
        e?.stopPropagation();
        console.log('Opening assignment modal...'); // Debug log
        setShowAssignmentModal(true);
        loadAssignments();
    };

    const handleSaveAssignments = () => {
        console.log('Saving assignments:', assignments); // Debug log
        setLoading(true);

        router.post('/admin/assignments', { assignments }, {
            onSuccess: () => {
                console.log('Assignments saved successfully');
                setShowAssignmentModal(false);
                setLoading(false);
                // Page will automatically refresh with updated data
            },
            onError: (errors) => {
                console.error('Save failed:', errors);
                setLoading(false);
                alert('Erreur lors de la sauvegarde des assignations: ' + (errors.message || 'Erreur inconnue'));
            },
            onFinish: () => {
                setLoading(false);
            }
        });
    };

    const updateAssignment = (assistantId, representativeIds) => {
        console.log(`Updating assignment for assistant ${assistantId}:`, representativeIds); // Debug log
        setAssignments(prev => {
            const updated = prev.map(assignment =>
                assignment.assistant_id === assistantId
                    ? { ...assignment, representative_ids: representativeIds }
                    : assignment
            );
            console.log('Updated assignments state:', updated); // Debug log
            return updated;
        });
    };

    const handlePageChange = (page) => {
        if (page < 1 || page > pairingHistory?.last_page) return;

        setCurrentPage(page);
        router.get('/admin/pairings', { page }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const PairingCard = ({ pairing }) => (
        <Card className="border-0 bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <Users className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">
                                {pairing.assistant_name} ↔ {pairing.representative_name}
                            </h3>
                            <p className="text-xs sm:text-sm text-gray-500">Pairing actuel</p>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                    <div className="text-center flex items-center flex-col p-2 sm:p-3 bg-blue-50 rounded-lg">
                        <p className="text-lg sm:text-2xl font-bold text-blue-600">{pairing.total_appointments || 0}</p>
                        <p className="text-xs sm:text-sm text-gray-500">RDV totales</p>
                    </div>
                    <div className="text-center p-2 sm:p-3 bg-green-50 rounded-lg">
                        <p className="text-lg sm:text-2xl font-bold text-green-600">{pairing.successful_appointments || 0}</p>
                        <p className="text-xs sm:text-sm text-gray-500">RDV Réussis</p>
                    </div>
                    <div className="text-center p-2 sm:p-3 bg-purple-50 rounded-lg">
                        <p className="text-lg sm:text-2xl font-bold text-purple-600">{pairing.success_rate || 0}%</p>
                        <p className="text-xs sm:text-sm text-gray-500">Taux Succès</p>
                    </div>
                    <div className="text-center p-2 sm:p-3 bg-orange-50 rounded-lg">
                        <p className="text-lg sm:text-2xl font-bold text-orange-600">
                            {pairing.total_revenue?.toLocaleString() || '0'} €
                        </p>
                        <p className="text-xs sm:text-sm text-gray-500">CA Généré</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const TopPairCard = ({ pair, rank }) => (
        <div className="flex items-center justify-between p-3 sm:p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-white font-bold text-xs sm:text-sm flex-shrink-0 ${
                    rank === 1 ? 'bg-yellow-500' :
                    rank === 2 ? 'bg-gray-400' :
                    rank === 3 ? 'bg-amber-600' : 'bg-blue-500'
                }`}>
                    {rank}
                </div>
                <div className="min-w-0 flex-1">
                    <p className="font-medium text-gray-900 text-xs sm:text-sm truncate">
                        {pair.assistant_name} & {pair.representative_name}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                        {pair.total_appointments} RDV • {pair.successful_appointments} RDV réussis
                    </p>
                </div>
            </div>
            <div className="text-right flex-shrink-0 ml-2">
                <p className="font-bold text-green-600 text-xs sm:text-sm">{pair.success_rate || 0}%</p>
                <p className="text-xs text-gray-500">
                    {pair.total_revenue?.toLocaleString() || '0'} €
                </p>
            </div>
        </div>
    );



    const AssignmentModal = () => (
        showAssignmentModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-lg p-4 sm:p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg sm:text-xl font-semibold">Gérer les Assignations SA ↔ SR</h3>
                        <button
                            onClick={() => setShowAssignmentModal(false)}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            ✕
                        </button>
                    </div>

                    <div className="space-y-6">
                        <p className="text-sm text-gray-600">
                            Assignez des représentants commerciaux à chaque assistant. Les assistants ne pourront choisir que parmi leurs représentants assignés lors de la création de rendez-vous.
                        </p>

                        {loading ? (
                            <div className="text-center py-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                                <p className="mt-2 text-gray-600">Chargement...</p>
                            </div>
                        ) : assignments.length === 0 ? (
                            <div className="text-center py-8">
                                <p className="text-gray-600">Aucune donnée d'assignation trouvée</p>
                                <p className="text-sm text-gray-500 mt-2">Vérifiez que des assistants existent dans le système</p>
                                <button
                                    onClick={loadAssignments}
                                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                >
                                    Recharger
                                </button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {assignments.map((assignment) => (
                                    <div key={assignment.assistant_id} className="border rounded-lg p-4 bg-gray-50">
                                        <div className="flex items-center justify-between mb-3">
                                            <h4 className="font-medium text-gray-900">
                                                {assignment.assistant_name}
                                            </h4>
                                            <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded">
                                                {assignment.representative_ids.length} assigné(s)
                                            </span>
                                        </div>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                                            {availableRepresentatives?.map((rep) => (
                                                <label key={rep.id} className="flex items-center space-x-2 p-2 rounded bg-white hover:bg-blue-50 transition-colors cursor-pointer">
                                                    <input
                                                        type="checkbox"
                                                        checked={assignment.representative_ids.includes(rep.id)}
                                                        onChange={(e) => {
                                                            const newIds = e.target.checked
                                                                ? [...assignment.representative_ids, rep.id]
                                                                : assignment.representative_ids.filter(id => id !== rep.id);
                                                            updateAssignment(assignment.assistant_id, newIds);
                                                        }}
                                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                    />
                                                    <span className="text-sm text-gray-700">{rep.name}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        <div className="flex justify-end space-x-3 pt-4 border-t">
                            <Button
                                variant="outline"
                                onClick={() => setShowAssignmentModal(false)}
                                disabled={loading}
                            >
                                Annuler
                            </Button>
                            <Button
                                onClick={handleSaveAssignments}
                                disabled={loading}
                                className="bg-blue-600 hover:bg-blue-700"
                            >
                                {loading ? 'Sauvegarde...' : 'Confirmer les Assignations'}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        )
    );

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Système de Pairing" />

            <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                {/* Header with Add Button */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Système de Pairing Hebdomadaire</h1>
                        <p className="text-sm sm:text-base text-gray-600 mt-1">Gérez les assignations SA ↔ SR et suivez leurs performances</p>
                    </div>
                    <Button
                        type="button"
                        onClick={handleOpenAssignmentModal}
                        variant="outline"
                        className="w-full sm:w-auto"
                    >
                        <Users className="h-4 w-4 mr-2" />
                        Gérer Assignations
                    </Button>
                </div>

                {/* Current Week Pairings */}
                <div>
                    <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                        <Calendar className="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" />
                        <span>Pairings de la Semaine Actuelle</span>
                    </h2>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                        {currentWeekPairings?.map((pairing, index) => (
                            <PairingCard key={index} pairing={pairing} showActions={true} />
                        ))}
                        {(!currentWeekPairings || currentWeekPairings.length === 0) && (
                            <div className="col-span-full text-center py-8 sm:py-12 text-gray-500">
                                <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">Aucun pairing actif</h3>
                                <p className="text-sm text-gray-500">Créez votre premier pairing pour cette semaine</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Top Performing Pairs */}
                <Card className="border-0 bg-white shadow-lg">
                    <CardHeader>
                        <CardTitle className="flex items-center text-[#525e62]">
                            <Award className="mr-2 h-5 w-5" />
                            Top Pairings Performants (30 derniers jours)
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {topPerformingPairs?.map((pair, index) => (
                                <TopPairCard key={index} pair={pair} rank={index + 1} />
                            ))}
                            {(!topPerformingPairs || topPerformingPairs.length === 0) && (
                                <div className="text-center py-8 text-gray-500">
                                    Aucune donnée de performance disponible
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Pairing History */}
                <Card className="border-0 bg-white shadow-lg">
                    <CardHeader>
                        <CardTitle className="flex items-center text-[#525e62]">
                            <History className="mr-2 h-5 w-5" />
                            Historique des Pairings
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                            <div className="min-w-[700px]">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b">
                                            <th className="text-left py-2 px-2">Date</th>
                                            <th className="text-left py-2 px-2">Assistant</th>
                                            <th className="text-left py-2 px-2">Représentant</th>
                                            <th className="text-center py-2 px-2">RDV</th>
                                            <th className="text-center py-2 px-2">Réussis</th>
                                            <th className="text-center py-2 px-2">CA</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {pairingHistory?.data?.map((history, index) => (
                                            <tr key={index} className="border-b hover:bg-gray-50">
                                                <td className="py-3 px-2 whitespace-nowrap">{history.date}</td>
                                                <td className="py-3 px-2">{history.assistant_name}</td>
                                                <td className="py-3 px-2">{history.representative_name}</td>
                                                <td className="text-center py-3 px-2">
                                                    <Badge variant="outline">{history.appointments_count}</Badge>
                                                </td>
                                                <td className="text-center py-3 px-2">
                                                    <Badge variant="outline" className="bg-green-50 text-green-700">
                                                        {history.purchases_count}
                                                    </Badge>
                                                </td>
                                                <td className="text-center py-3 px-2">
                                                    <span className="font-bold text-green-600 whitespace-nowrap">
                                                        {history.revenue?.toLocaleString()} €
                                                    </span>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                                {(!pairingHistory?.data || pairingHistory.data.length === 0) && (
                                    <div className="text-center py-8 text-gray-500">
                                        Aucun historique disponible
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Pagination Controls */}
                        {pairingHistory?.total > 0 && (
                            <div className="flex flex-col sm:flex-row items-center justify-between mt-4 pt-4 border-t space-y-2 sm:space-y-0">
                                <div className="text-sm text-gray-600">
                                    Affichage de {pairingHistory.from} à {pairingHistory.to} sur {pairingHistory.total} résultats
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(currentPage - 1)}
                                        disabled={currentPage <= 1}
                                        className="px-3 py-1"
                                    >
                                        Précédent
                                    </Button>

                                    <div className="flex items-center space-x-1">
                                        {Array.from({ length: Math.min(5, pairingHistory.last_page) }, (_, i) => {
                                            const page = i + 1;
                                            return (
                                                <Button
                                                    key={page}
                                                    variant={currentPage === page ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => handlePageChange(page)}
                                                    className="px-3 py-1"
                                                >
                                                    {page}
                                                </Button>
                                            );
                                        })}
                                        {pairingHistory.last_page > 5 && (
                                            <>
                                                <span className="px-2">...</span>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handlePageChange(pairingHistory.last_page)}
                                                    className="px-3 py-1"
                                                >
                                                    {pairingHistory.last_page}
                                                </Button>
                                            </>
                                        )}
                                    </div>

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(currentPage + 1)}
                                        disabled={currentPage >= pairingHistory.last_page}
                                        className="px-3 py-1"
                                    >
                                        Suivant
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            <AssignmentModal />
        </AppLayout>
    );
}
