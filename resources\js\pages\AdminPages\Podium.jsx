import React, { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Award, 
    TrendingUp, 
    Calendar,
    DollarSign,
    Trophy,
    Medal,
    Crown,
    RefreshCw,
    Users,
    Target
} from 'lucide-react';

const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Gestion du podium',
        href: '/admin/podium',
    },
];

export default function AdminPodium() {
    const { revenueLeaderboard, appointmentsLeaderboard, bonusesLeaderboard } = usePage().props;
    const [lastUpdated, setLastUpdated] = useState(new Date());
    const [autoRefresh, setAutoRefresh] = useState(true);

    useEffect(() => {
        let interval;
        if (autoRefresh) {
            interval = setInterval(() => {
                router.reload({ only: ['revenueLeaderboard', 'appointmentsLeaderboard', 'bonusesLeaderboard'] });
                setLastUpdated(new Date());
            }, 30000); // Refresh every 30 seconds
        }
        return () => clearInterval(interval);
    }, [autoRefresh]);

    const handleManualRefresh = () => {
        router.reload({ only: ['revenueLeaderboard', 'appointmentsLeaderboard', 'bonusesLeaderboard'] });
        setLastUpdated(new Date());
    };

    const getRankIcon = (rank) => {
        switch (rank) {
            case 1:
                return <Crown className="h-6 w-6 text-yellow-500" />;
            case 2:
                return <Medal className="h-6 w-6 text-gray-400" />;
            case 3:
                return <Award className="h-6 w-6 text-amber-600" />;
            default:
                return <Trophy className="h-6 w-6 text-blue-500" />;
        }
    };

    const getRankBadge = (rank) => {
        const rankConfig = {
            1: { className: 'bg-yellow-100 text-yellow-800 border-yellow-300', label: '1er' },
            2: { className: 'bg-gray-100 text-gray-800 border-gray-300', label: '2ème' },
            3: { className: 'bg-amber-100 text-amber-800 border-amber-300', label: '3ème' },
        };
        
        const config = rankConfig[rank] || { 
            className: 'bg-blue-100 text-blue-800 border-blue-300', 
            label: `${rank}ème` 
        };
        
        return (
            <Badge variant="outline" className={`${config.className} font-bold`}>
                {config.label}
            </Badge>
        );
    };

    const LeaderboardCard = ({ title, data, type, icon: Icon, color }) => (
        <Card className="h-full border-0 bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
                <CardTitle className={`flex items-center ${color} text-sm sm:text-base`}>
                    <Icon className="mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                    <span className="truncate">{title}</span>
                </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="space-y-2 sm:space-y-3">
                    {data?.slice(0, 10).map((item, index) => {
                        const rank = index + 1;
                        return (
                            <div
                                key={item.id}
                                className={`flex items-center justify-between p-2 sm:p-3 lg:p-4 rounded-lg transition-all hover:shadow-sm ${
                                    rank <= 3 ? 'bg-gradient-to-r from-gray-50 to-white border border-gray-200' : 'bg-gray-50'
                                }`}
                            >
                                <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-4 min-w-0 flex-1">
                                    <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                                        {getRankIcon(rank)}
                                        <div className="hidden sm:block">
                                            {getRankBadge(rank)}
                                        </div>
                                    </div>
                                    <div className="min-w-0 flex-1">
                                        <h3 className="font-semibold text-gray-900 text-xs sm:text-sm lg:text-base truncate">{item.name}</h3>
                                        <p className="text-xs text-gray-500 truncate hidden sm:block">{item.email}</p>
                                    </div>
                                </div>
                                <div className="text-right flex-shrink-0 ml-2">
                                    {type === 'revenue' && (
                                        <div>
                                            <p className="text-sm sm:text-lg lg:text-xl font-bold text-green-600">
                                                {item.weekly_revenue?.toLocaleString() || 0} €
                                            </p>
                                            <p className="text-xs text-gray-500 hidden sm:block">CA Semaine</p>
                                        </div>
                                    )}
                                    {type === 'appointments' && (
                                        <div>
                                            <p className="text-sm sm:text-lg lg:text-xl font-bold text-blue-600">
                                                {item.weekly_appointments || 0}
                                            </p>
                                            <p className="text-xs text-gray-500 hidden sm:block">RDV Semaine</p>
                                        </div>
                                    )}
                                    {type === 'bonuses' && (
                                        <div>
                                            <p className="text-sm sm:text-lg lg:text-xl font-bold text-purple-600">
                                                {item.weekly_bonuses?.toLocaleString() || 0} €
                                            </p>
                                            <p className="text-xs text-gray-500 hidden sm:block">Bonus Semaine</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })}

                    {(!data || data.length === 0) && (
                        <div className="text-center py-6 sm:py-8 text-gray-500">
                            <Icon className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 sm:mb-4 text-gray-300" />
                            <p className="text-sm">Aucune donnée disponible</p>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );

    const TopPerformerSpotlight = ({ title, performer, type, icon: Icon, color }) => {
        if (!performer) return (
            <Card className={`h-full border-0 shadow-sm bg-gradient-to-br from-white to-gray-50 ${color}`}>
                <CardContent className="p-4 sm:p-6 lg:p-8 text-center flex items-center justify-center min-h-[200px]">
                    <div className="text-gray-400">
                        <Icon className="h-12 w-12 mx-auto mb-2" />
                        <p className="text-sm">Aucun champion</p>
                    </div>
                </CardContent>
            </Card>
        );

        return (
            <Card className={`h-full border-0 shadow-sm hover:shadow-md transition-shadow bg-gradient-to-br from-white to-gray-50 ${color}`}>
                <CardContent className="p-4 sm:p-6 lg:p-8 text-center">
                    <div className="mb-3 sm:mb-4">
                        <Crown className="h-8 w-8 sm:h-12 sm:w-12 lg:h-16 lg:w-16 text-yellow-500 mx-auto mb-2" />
                        <h3 className="text-base sm:text-lg lg:text-2xl font-bold text-gray-900">{title}</h3>
                    </div>

                    <div className="mb-4 sm:mb-6">
                        <h2 className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-1 sm:mb-2 truncate">{performer.name}</h2>
                        <p className="text-xs sm:text-sm lg:text-base text-gray-600 truncate">{performer.email}</p>
                    </div>

                    <div className="flex items-center justify-center space-x-1 sm:space-x-2 mb-3 sm:mb-4">
                        <Icon className="h-4 w-4 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-yellow-500 flex-shrink-0" />
                        {type === 'revenue' && (
                            <span className="text-lg sm:text-2xl lg:text-4xl font-bold text-green-600 truncate">
                                {performer.weekly_revenue?.toLocaleString() || 0} €
                            </span>
                        )}
                        {type === 'appointments' && (
                            <span className="text-lg sm:text-2xl lg:text-4xl font-bold text-blue-600">
                                {performer.weekly_appointments || 0} RDV
                            </span>
                        )}
                        {type === 'bonuses' && (
                            <span className="text-lg sm:text-2xl lg:text-4xl font-bold text-purple-600 truncate">
                                {performer.weekly_bonuses?.toLocaleString() || 0} €
                            </span>
                        )}
                    </div>

                    <Badge className="bg-yellow-100 text-yellow-800 text-xs sm:text-sm lg:text-base px-2 sm:px-3 lg:px-4 py-1 sm:py-2">
                        🏆 Champion de la semaine
                    </Badge>
                </CardContent>
            </Card>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Gestion du Podium" />

            <div className="space-y-4 sm:space-y-6 lg:space-y-8 p-4 sm:p-6">
                {/* Header */}
                <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">🏆 Podium en Temps Réel</h1>
                        <p className="text-sm sm:text-base text-gray-600 mt-1 sm:mt-2">
                            Classements mis à jour automatiquement •
                            Dernière mise à jour: {lastUpdated.toLocaleTimeString('fr-FR')}
                        </p>
                    </div>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 w-full lg:w-auto">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="autoRefresh"
                                checked={autoRefresh}
                                onChange={(e) => setAutoRefresh(e.target.checked)}
                                className="rounded"
                            />
                            <label htmlFor="autoRefresh" className="text-xs sm:text-sm text-gray-600">
                                Actualisation auto
                            </label>
                        </div>
                        <Button variant="outline" onClick={handleManualRefresh} className="w-full sm:w-auto">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Actualiser
                        </Button>
                    </div>
                </div>

                {/* Top Performers Spotlight */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                    <TopPerformerSpotlight
                        title="🥇 Top CA"
                        performer={revenueLeaderboard?.[0]}
                        type="revenue"
                        icon={DollarSign}
                        color="border-l-4 border-green-500"
                    />
                    <TopPerformerSpotlight
                        title="🥇 Top RDV"
                        performer={appointmentsLeaderboard?.[0]}
                        type="appointments"
                        icon={Calendar}
                        color="border-l-4 border-blue-500"
                    />
                    <TopPerformerSpotlight
                        title="🥇 Top Bonus"
                        performer={bonusesLeaderboard?.[0]}
                        type="bonuses"
                        icon={Award}
                        color="border-l-4 border-purple-500"
                    />
                </div>

                {/* Detailed Leaderboards */}
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    <LeaderboardCard
                        title="🏆 Classement CA (Représentants)"
                        data={revenueLeaderboard}
                        type="revenue"
                        icon={DollarSign}
                        color="text-green-600"
                    />
                    <LeaderboardCard
                        title="🏆 Classement RDV (Assistants)"
                        data={appointmentsLeaderboard}
                        type="appointments"
                        icon={Calendar}
                        color="text-blue-600"
                    />
                    <LeaderboardCard
                        title="🏆 Classement Bonus (Assistants)"
                        data={bonusesLeaderboard}
                        type="bonuses"
                        icon={Award}
                        color="text-purple-600"
                    />
                </div>
            </div>
        </AppLayout>
    );
}
