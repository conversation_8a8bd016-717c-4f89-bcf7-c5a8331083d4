import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Award, DollarSign, CheckCircle, Target, Trophy, TrendingUp, Phone } from "lucide-react"

const breadcrumbs = [
    {
        title: 'Performance de l’assistant',
        href: '/assistantPerformance',
    },
];

export default function AssistantPerformance() {

    const getRankColor = (rank) => {
        switch (rank) {
            case 1:
                return "bg-yellow-500"
            case 2:
                return "bg-gray-400"
            case 3:
                return "bg-orange-400"
            default:
                return "bg-[#525e62]"
        }
    }

    const { auth, assistantsLeaderboard, dailyBonuses, weeklyBonuses, announcedTodayAppointments, weeklyAnnouncedAppointmentsTotal } = usePage().props

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="AssistantPerformance" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    <div>
                        <h1 className="text-3xl font-bold text-[#525e62]">Performance</h1>
                        <p className="text-[#525e62]/70">Analysez vos performances et suivez vos indicateurs clés.</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card className="bg-white border-0 shadow-lg">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">Rendez-vous annoncés aujourd'hui</p>
                                        <p className="text-2xl font-bold text-[#525e62] mt-1">{announcedTodayAppointments}</p>
                                    </div>
                                    <div className="p-3 rounded-full bg-green-500">
                                        <Phone className="h-6 w-6 text-white" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-lg">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">Rendez-vous annoncés cette semaine</p>
                                        <p className="text-2xl font-bold text-[#525e62] mt-1">{weeklyAnnouncedAppointmentsTotal}</p>
                                    </div>
                                    <div className="p-3 rounded-full bg-green-500">
                                        <Phone className="h-6 w-6 text-white" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="bg-white border-0 shadow-lg">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">Bonus du jour</p>
                                        <p className="text-2xl font-bold text-[#525e62] mt-1">{dailyBonuses}</p>
                                    </div>
                                    <div className="p-3 rounded-full bg-purple-500">
                                        <Award className="h-6 w-6 text-white" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-lg">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">Bonus de la semaine</p>
                                        <p className="text-2xl font-bold text-[#525e62] mt-1">{weeklyBonuses}</p>
                                    </div>
                                    <div className="p-3 rounded-full bg-purple-500">
                                        <Award className="h-6 w-6 text-white" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                        <Card className="bg-white border-0 shadow-lg">
                            <CardHeader>
                                <CardTitle className="text-[#525e62] flex items-center">
                                    <Trophy className="mr-2 h-5 w-5" />
                                    Classement hebdomadaire des rendez-vous
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {
                                    assistantsLeaderboard[0] ?
                                        <div className="space-y-3">
                                            {assistantsLeaderboard.map((assistant) => (
                                                <div
                                                    key={assistantsLeaderboard.indexOf(assistant)}
                                                    className={`flex items-center space-x-3 p-3 rounded-lg transition-all ${assistant.id === auth.user.id ? "bg-[#525e62]/10 border-2 border-[#525e62]/30" : "bg-[#f1efe0]/30"
                                                        }`}
                                                >
                                                    <div
                                                        className={`w-8 h-8 ${getRankColor(assistantsLeaderboard.indexOf(assistant) + 1)} rounded-full flex items-center justify-center text-white font-bold text-sm`}
                                                    >
                                                        {assistantsLeaderboard.indexOf(assistant) + 1}
                                                    </div>
                                                    <div className="flex-1">
                                                        <p className={`font-medium ${assistant.name === auth.user.name ? "text-[#525e62] font-bold" : "text-[#525e62]"}`}>
                                                            {assistant.id === auth.user.id ? "Vous" : assistant.name}
                                                        </p>
                                                    </div>
                                                </div>
                                            ))

                                            }
                                        </div>
                                        :
                                        <div className='flex items-center justify-center h-[20vh] w-full border-[#525e62]/10 rounded-lg  hover:bg-[#f1efe0]/30 transition-colors p-4 '>
                                            <h1>Aucun rendez-vous pour l'instant</h1>
                                        </div>
                                }
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
};
