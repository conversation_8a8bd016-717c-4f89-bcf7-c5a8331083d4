<?php

use App\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified', 'activated', 'role:admin'])->prefix('admin')->group(function () {
    
    // Global Dashboard
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('admin.dashboard');
    
    // Weekly Pairing System
    Route::get('/pairings', [AdminController::class, 'pairings'])->name('admin.pairings');
    Route::post('/pairings/assign', [AdminController::class, 'assignPairing'])->name('admin.pairings.assign');
    Route::put('/pairings/{id}', [AdminController::class, 'updatePairing'])->name('admin.pairings.update');
    Route::delete('/pairings/{id}', [AdminController::class, 'deletePairing'])->name('admin.pairings.delete');
    
    // Client & Appointment Database
    Route::get('/appointments', [AdminController::class, 'appointments'])->name('admin.appointments');
    Route::get('/appointments/export', [AdminController::class, 'exportAppointments'])->name('admin.appointments.export');
    Route::get('/appointments/{id}', [AdminController::class, 'showAppointment'])->name('admin.appointments.show');
    Route::put('/appointments/{id}', [AdminController::class, 'updateAppointment'])->name('admin.appointments.update');
    Route::delete('/appointments/{id}', [AdminController::class, 'deleteAppointment'])->name('admin.appointments.delete');
    
    // Bonus System Oversight
    Route::get('/bonuses', [AdminController::class, 'bonuses'])->name('admin.bonuses');
    Route::put('/bonuses/{id}', [AdminController::class, 'updateBonus'])->name('admin.bonuses.update');
    Route::delete('/bonuses/{id}', [AdminController::class, 'deleteBonus'])->name('admin.bonuses.delete');
    Route::get('/bonuses/export', [AdminController::class, 'exportBonuses'])->name('admin.bonuses.export');
    
    // Podium Management
    Route::get('/podium', [AdminController::class, 'podium'])->name('admin.podium');
    Route::get('/podium/data', [AdminController::class, 'podiumData'])->name('admin.podium.data');
    
    // User & Role Management
    Route::get('/users', [AdminController::class, 'users'])->name('admin.users');
    Route::post('/users', [AdminController::class, 'createUser'])->name('admin.users.create');
    Route::put('/users/{id}', [AdminController::class, 'updateUser'])->name('admin.users.update');
    Route::delete('/users/{id}', [AdminController::class, 'deleteUser'])->name('admin.users.delete');
    Route::post('/users/{id}/reset-password', [AdminController::class, 'resetPassword'])->name('admin.users.reset-password');
    Route::post('/users/{id}/toggle-status', [AdminController::class, 'toggleUserStatus'])->name('admin.users.toggle-status');

    // Assignment Management
    Route::get('/assignments', [AdminController::class, 'getAssignments'])->name('admin.assignments.get');
    Route::post('/assignments', [AdminController::class, 'saveAssignments'])->name('admin.assignments.save');

});
